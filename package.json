{"name": "clean-architecture-nest", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "engines": {"node": ">=22.11.x", "npm": "10.x"}, "type": "commonjs", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start:dev": "env-cmd --silent cross-env NODE_ENV=development nest start --watch", "start:debug": "env-cmd --silent cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "env-cmd --silent cross-env NODE_ENV=production node dist/infra/framework/main", "migrate:dev": "env-cmd --silent prisma migrate dev", "migrate:prod": "env-cmd --silent prisma migrate deploy", "migrate:test": "env-cmd --silent sh -c 'cross-env DATABASE_URL=$TEST_DATABASE_URL prisma migrate deploy'", "nest-commands": "env-cmd --silent ts-node -r tsconfig-paths/register -P tsconfig.json scripts/run-nest-commands.ts", "rabbitmq:migrate": "yarn run nest-commands rabbitmq:migrate", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "env-cmd --silent cross-env NODE_ENV=test jest", "test:watch": "env-cmd --silent cross-env NODE_ENV=test jest --watch", "test:cov": "env-cmd --silent cross-env NODE_ENV=test jest --coverage", "test:debug": "env-cmd --silent cross-env NODE_ENV=test node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "env-cmd --silent cross-env NODE_ENV=test jest --config ./test/jest-e2e.json --detectOpenHandles --forceExit"}, "dependencies": {"@nestjs/axios": "^3.0.2", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/cqrs": "^10.2.5", "@nestjs/mapped-types": "^2.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^3.0.4", "@nestjs/swagger": "^7.1.8", "@nestjs/terminus": "^10.1.1", "@ntegral/nestjs-sentry": "^4.0.0", "@prisma/client": "^5.13.0", "@sentry/node": "^7.64.0", "@sw-web/nestjs-core": "^1.14.1", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "axios": "^1.7.2", "body-parser": "^1.20.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "crypto": "^1.0.1", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "deep-diff": "^1.0.2", "env-cmd": "^10.1.0", "env-var": "^7.4.0", "eventemitter2": "^6.4.9", "express": "^4.18.2", "lodash": "^4.17.21", "logform": "^2.5.1", "nest-commander": "^3.15.0", "pg": "^8.13.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "ua-parser-js": "^1.0.39", "ulid": "^2.3.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@nestjs/cli": "^10.1.17", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@suites/di.nestjs": "^3.0.1", "@suites/doubles.jest": "^3.0.1", "@suites/unit": "^3.0.1", "@types/amqplib": "^0.10.2", "@types/cookie-parser": "^1.4.3", "@types/cron": "^2.4.0", "@types/deep-diff": "^1.0.5", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.197", "@types/node": "^20.3.1", "@types/pg": "^8.11.10", "@types/supertest": "^2.0.12", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.2.3", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.13.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "prisma": {"schema": "src/infra/drivers/prisma/schema.prisma", "seed": "ts-node -r tsconfig-paths/register src/infra/drivers/prisma/seed-dev.ts"}}