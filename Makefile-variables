-include .env

# Define exec params per env;
ifeq ($(NODE_ENV), test)
  export DOCKER_COMPOSE_FILE = docker-compose.test.yml
  export USE_LEGACY_DOCKER_COMPOSE = false
else ifeq ($(NODE_ENV), development)
  export DOCKER_COMPOSE_FILE = docker-compose.dev.yml
  export USE_LEGACY_DOCKER_COMPOSE = false
else ifeq ($(NODE_ENV), production)
  export DOCKER_COMPOSE_FILE = docker-compose.prod.yml
  export USE_LEGACY_DOCKER_COMPOSE = true
else ifeq ($(NODE_ENV), local)
  export DOCKER_COMPOSE_FILE = docker-compose.local.yml
  export USE_LEGACY_DOCKER_COMPOSE = false
else
  $(error NODE_ENV is not set or has an invalid value)
endif