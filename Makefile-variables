-include .env

# Define exec params per env;
ifeq ($(NODE_ENV), test)
  export DOCKER_COMPOSE_FILE = docker-compose.test.yml
else ifeq ($(NODE_ENV), development)
  export DOCKER_COMPOSE_FILE = docker-compose.dev.yml
else ifeq ($(NODE_ENV), production)
  export DOCKER_COMPOSE_FILE = docker-compose.prod.yml
else ifeq ($(NODE_ENV), local)
  export DOCKER_COMPOSE_FILE = docker-compose.local.yml
else
  $(error NODE_ENV is not set or has an invalid value)
endif