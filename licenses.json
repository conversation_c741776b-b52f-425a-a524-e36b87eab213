{"@acuminous/bitsyntax@0.1.2": {"licenses": "MIT", "repository": "https://github.com/acuminous/bitsyntax-js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@acuminous/bitsyntax", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@acuminous/bitsyntax/LICENSE"}, "@ampproject/remapping@2.3.0": {"licenses": "Apache-2.0", "repository": "https://github.com/ampproject/remapping", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ampproject/remapping", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ampproject/remapping/LICENSE"}, "@angular-devkit/core@17.1.2": {"licenses": "MIT", "repository": "https://github.com/angular/angular-cli", "publisher": "Angular Authors", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/core/LICENSE"}, "@angular-devkit/schematics-cli@17.1.2": {"licenses": "MIT", "repository": "https://github.com/angular/angular-cli", "publisher": "Angular Authors", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/schematics-cli", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/schematics-cli/LICENSE"}, "@angular-devkit/schematics@17.1.2": {"licenses": "MIT", "repository": "https://github.com/angular/angular-cli", "publisher": "Angular Authors", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/schematics", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/schematics/LICENSE"}, "@babel/code-frame@7.24.2": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/code-frame", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/code-frame/LICENSE"}, "@babel/compat-data@7.24.4": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/compat-data", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/compat-data/LICENSE"}, "@babel/core@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/core/LICENSE"}, "@babel/generator@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/generator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/generator/LICENSE"}, "@babel/helper-compilation-targets@7.23.6": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-compilation-targets", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-compilation-targets/LICENSE"}, "@babel/helper-environment-visitor@7.22.20": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-environment-visitor", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-environment-visitor/LICENSE"}, "@babel/helper-function-name@7.23.0": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-function-name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-function-name/LICENSE"}, "@babel/helper-hoist-variables@7.22.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-hoist-variables", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-hoist-variables/LICENSE"}, "@babel/helper-module-imports@7.24.3": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-module-imports", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-module-imports/LICENSE"}, "@babel/helper-module-transforms@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-module-transforms", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-module-transforms/LICENSE"}, "@babel/helper-plugin-utils@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-plugin-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-plugin-utils/LICENSE"}, "@babel/helper-simple-access@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-simple-access", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-simple-access/LICENSE"}, "@babel/helper-split-export-declaration@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-split-export-declaration", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-split-export-declaration/LICENSE"}, "@babel/helper-string-parser@7.24.1": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-string-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-string-parser/LICENSE"}, "@babel/helper-validator-identifier@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-validator-identifier", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-validator-identifier/LICENSE"}, "@babel/helper-validator-option@7.23.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-validator-option", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-validator-option/LICENSE"}, "@babel/helpers@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helpers", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helpers/LICENSE"}, "@babel/highlight@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/LICENSE"}, "@babel/parser@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/parser/LICENSE"}, "@babel/plugin-syntax-async-generators@7.8.4": {"licenses": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-async-generators", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-async-generators/LICENSE"}, "@babel/plugin-syntax-bigint@7.8.3": {"licenses": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-bigint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-bigint", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-bigint/LICENSE"}, "@babel/plugin-syntax-class-properties@7.12.13": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-class-properties", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-class-properties/LICENSE"}, "@babel/plugin-syntax-import-meta@7.10.4": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-import-meta", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-import-meta/LICENSE"}, "@babel/plugin-syntax-json-strings@7.8.3": {"licenses": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-json-strings", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-json-strings/LICENSE"}, "@babel/plugin-syntax-jsx@7.24.1": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-jsx", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-jsx/LICENSE"}, "@babel/plugin-syntax-logical-assignment-operators@7.10.4": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-logical-assignment-operators", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-logical-assignment-operators/LICENSE"}, "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3": {"licenses": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-nullish-coalescing-operator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-nullish-coalescing-operator/LICENSE"}, "@babel/plugin-syntax-numeric-separator@7.10.4": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-numeric-separator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-numeric-separator/LICENSE"}, "@babel/plugin-syntax-object-rest-spread@7.8.3": {"licenses": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-object-rest-spread", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-object-rest-spread/LICENSE"}, "@babel/plugin-syntax-optional-catch-binding@7.8.3": {"licenses": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-optional-catch-binding", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-optional-catch-binding/LICENSE"}, "@babel/plugin-syntax-optional-chaining@7.8.3": {"licenses": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-optional-chaining", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-optional-chaining/LICENSE"}, "@babel/plugin-syntax-top-level-await@7.14.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-top-level-await", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-top-level-await/LICENSE"}, "@babel/plugin-syntax-typescript@7.24.1": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-typescript", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/plugin-syntax-typescript/LICENSE"}, "@babel/template@7.24.0": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/template", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/template/LICENSE"}, "@babel/traverse@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/traverse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/traverse/LICENSE"}, "@babel/types@7.24.5": {"licenses": "MIT", "repository": "https://github.com/babel/babel", "publisher": "The Babel Team", "url": "https://babel.dev/team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/types/LICENSE"}, "@bcoe/v8-coverage@0.2.3": {"licenses": "MIT", "repository": "https://github.com/demurgos/v8-coverage", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://demurgos.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@bcoe/v8-coverage", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@bcoe/v8-coverage/LICENSE.md"}, "@colors/colors@1.5.0": {"licenses": "MIT", "repository": "https://github.com/DABH/colors.js", "publisher": "DABH", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@colors/colors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@colors/colors/LICENSE"}, "@colors/colors@1.6.0": {"licenses": "MIT", "repository": "https://github.com/DABH/colors.js", "publisher": "DABH", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/logform/node_modules/@colors/colors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/logform/node_modules/@colors/colors/LICENSE"}, "@cspotcode/source-map-support@0.8.1": {"licenses": "MIT", "repository": "https://github.com/cspotcode/node-source-map-support", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@cspotcode/source-map-support", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@cspotcode/source-map-support/LICENSE.md"}, "@dabh/diagnostics@2.0.3": {"licenses": "MIT", "repository": "https://github.com/3rd-Eden/diagnostics", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@dabh/diagnostics", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@dabh/diagnostics/LICENSE"}, "@eslint-community/eslint-utils@4.4.0": {"licenses": "MIT", "repository": "https://github.com/eslint-community/eslint-utils", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint-community/eslint-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint-community/eslint-utils/LICENSE"}, "@eslint-community/regexpp@4.10.0": {"licenses": "MIT", "repository": "https://github.com/eslint-community/regexpp", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint-community/regexpp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint-community/regexpp/LICENSE"}, "@eslint/eslintrc@2.1.4": {"licenses": "MIT", "repository": "https://github.com/eslint/eslintrc", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint/eslintrc", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint/eslintrc/LICENSE"}, "@eslint/js@8.57.0": {"licenses": "MIT", "repository": "https://github.com/eslint/eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint/js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@eslint/js/LICENSE"}, "@fig/complete-commander@3.2.0": {"licenses": "MIT", "repository": "https://github.com/withfig/autocomplete-tools", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/nest-commander/node_modules/@fig/complete-commander", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/nest-commander/node_modules/@fig/complete-commander/LICENSE"}, "@golevelup/nestjs-discovery@4.0.1": {"licenses": "MIT", "repository": "https://github.com/golevelup/nestjs", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@golevelup/nestjs-discovery", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@golevelup/nestjs-discovery/LICENSE"}, "@graphql-tools/merge@9.0.0": {"licenses": "MIT", "repository": "https://github.com/ardatan/graphql-tools", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@graphql-tools/merge", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@graphql-tools/merge/README.md"}, "@graphql-tools/schema@10.0.0": {"licenses": "MIT", "repository": "https://github.com/ardatan/graphql-tools", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@graphql-tools/schema", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@graphql-tools/schema/README.md"}, "@graphql-tools/utils@10.0.8": {"licenses": "MIT", "repository": "https://github.com/ardatan/graphql-tools", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@graphql-tools/utils"}, "@graphql-typed-document-node/core@3.2.0": {"licenses": "MIT", "repository": "https://github.com/dotansimha/graphql-typed-document-node", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@graphql-typed-document-node/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@graphql-typed-document-node/core/LICENSE"}, "@humanwhocodes/config-array@0.11.14": {"licenses": "Apache-2.0", "repository": "https://github.com/humanwhocodes/config-array", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@humanwhocodes/config-array", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@humanwhocodes/config-array/LICENSE"}, "@humanwhocodes/module-importer@1.0.1": {"licenses": "Apache-2.0", "repository": "https://github.com/humanwhocodes/module-importer", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@humanwhocodes/module-importer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@humanwhocodes/module-importer/LICENSE"}, "@humanwhocodes/object-schema@2.0.3": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/humanwhocodes/object-schema", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@humanwhocodes/object-schema", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@humanwhocodes/object-schema/LICENSE"}, "@iarna/toml@2.2.5": {"licenses": "ISC", "repository": "https://github.com/iarna/iarna-toml", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@iarna/toml", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@iarna/toml/LICENSE"}, "@isaacs/cliui@8.0.2": {"licenses": "ISC", "repository": "https://github.com/yargs/cliui", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@isaacs/cliui", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@isaacs/cliui/LICENSE.txt"}, "@istanbuljs/load-nyc-config@1.1.0": {"licenses": "ISC", "repository": "https://github.com/istanbuljs/load-nyc-config", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/LICENSE"}, "@istanbuljs/schema@0.1.3": {"licenses": "MIT", "repository": "https://github.com/istanbuljs/schema", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/schema", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/schema/LICENSE"}, "@jest/console@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/console", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/console/LICENSE"}, "@jest/core@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/core/LICENSE"}, "@jest/environment@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/environment", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/environment/LICENSE"}, "@jest/expect-utils@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/expect-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/expect-utils/LICENSE"}, "@jest/expect@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/expect", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/expect/LICENSE"}, "@jest/fake-timers@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/fake-timers", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/fake-timers/LICENSE"}, "@jest/globals@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/globals", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/globals/LICENSE"}, "@jest/reporters@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/reporters", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/reporters/LICENSE"}, "@jest/schemas@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/schemas", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/schemas/LICENSE"}, "@jest/source-map@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/source-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/source-map/LICENSE"}, "@jest/test-result@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/test-result", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/test-result/LICENSE"}, "@jest/test-sequencer@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/test-sequencer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/test-sequencer/LICENSE"}, "@jest/transform@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/transform", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/transform/LICENSE"}, "@jest/types@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jest/types/LICENSE"}, "@jridgewell/gen-mapping@0.3.5": {"licenses": "MIT", "repository": "https://github.com/jridgewell/gen-mapping", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/gen-mapping", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/gen-mapping/LICENSE"}, "@jridgewell/resolve-uri@3.1.2": {"licenses": "MIT", "repository": "https://github.com/jridgewell/resolve-uri", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/resolve-uri", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/resolve-uri/LICENSE"}, "@jridgewell/set-array@1.2.1": {"licenses": "MIT", "repository": "https://github.com/jridgewell/set-array", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/set-array", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/set-array/LICENSE"}, "@jridgewell/source-map@0.3.6": {"licenses": "MIT", "repository": "https://github.com/jridgewell/source-map", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/source-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/source-map/LICENSE"}, "@jridgewell/sourcemap-codec@1.4.15": {"licenses": "MIT", "repository": "https://github.com/jridgewell/sourcemap-codec", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/sourcemap-codec", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/sourcemap-codec/LICENSE"}, "@jridgewell/trace-mapping@0.3.25": {"licenses": "MIT", "repository": "https://github.com/jridgewell/trace-mapping", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/trace-mapping", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@jridgewell/trace-mapping/LICENSE"}, "@jridgewell/trace-mapping@0.3.9": {"licenses": "MIT", "repository": "https://github.com/jridgewell/trace-mapping", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping/LICENSE"}, "@ljharb/through@2.3.13": {"licenses": "MIT", "repository": "https://github.com/ljharb/through", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ljharb/through", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ljharb/through/LICENSE"}, "@lukeed/csprng@1.1.0": {"licenses": "MIT", "repository": "https://github.com/lukeed/csprng", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@lukeed/csprng", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@lukeed/csprng/license"}, "@microsoft/tsdoc@0.14.2": {"licenses": "MIT", "repository": "https://github.com/microsoft/tsdoc", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@microsoft/tsdoc", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@microsoft/tsdoc/LICENSE"}, "@nestjs/axios@3.0.2": {"licenses": "MIT", "repository": "https://github.com/nestjs/axios", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/axios", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/axios/LICENSE"}, "@nestjs/cli@10.3.2": {"licenses": "MIT", "repository": "https://github.com/nestjs/nest-cli", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/LICENSE"}, "@nestjs/common@10.3.8": {"licenses": "MIT", "repository": "https://github.com/nestjs/nest", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/common", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/common/LICENSE"}, "@nestjs/core@10.3.8": {"licenses": "MIT", "repository": "https://github.com/nestjs/nest", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/core/LICENSE"}, "@nestjs/cqrs@10.2.7": {"licenses": "MIT", "repository": "https://github.com/nestjs/cqrs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cqrs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cqrs/LICENSE"}, "@nestjs/graphql@12.0.11": {"licenses": "MIT", "repository": "https://github.com/nestjs/graphql", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/graphql", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/graphql/LICENSE"}, "@nestjs/mapped-types@2.0.2": {"licenses": "MIT", "repository": "https://github.com/nestjs/mapped-types", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/graphql/node_modules/@nestjs/mapped-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/graphql/node_modules/@nestjs/mapped-types/LICENSE"}, "@nestjs/mapped-types@2.0.5": {"licenses": "MIT", "repository": "https://github.com/nestjs/mapped-types", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/mapped-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/mapped-types/LICENSE"}, "@nestjs/platform-express@10.3.8": {"licenses": "MIT", "repository": "https://github.com/nestjs/nest", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/platform-express", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/platform-express/LICENSE"}, "@nestjs/schedule@3.0.4": {"licenses": "MIT", "repository": "https://github.com/nestjs/schedule", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/schedule", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/schedule/LICENSE"}, "@nestjs/schematics@10.1.1": {"licenses": "MIT", "repository": "https://github.com/nestjs/schematics", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/schematics", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/schematics/LICENSE"}, "@nestjs/swagger@7.3.1": {"licenses": "MIT", "repository": "https://github.com/nestjs/swagger", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/swagger", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/swagger/LICENSE"}, "@nestjs/terminus@10.2.3": {"licenses": "MIT", "repository": "https://github.com/nestjs/terminus", "publisher": "Livio B<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/terminus", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/terminus/LICENSE"}, "@nestjs/testing@10.3.8": {"licenses": "MIT", "repository": "https://github.com/nestjs/nest", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/testing", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/testing/LICENSE"}, "@nodelib/fs.scandir@2.1.5": {"licenses": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nodelib/fs.scandir", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nodelib/fs.scandir/LICENSE"}, "@nodelib/fs.stat@2.0.5": {"licenses": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nodelib/fs.stat", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nodelib/fs.stat/LICENSE"}, "@nodelib/fs.walk@1.2.8": {"licenses": "MIT", "repository": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nodelib/fs.walk", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nodelib/fs.walk/LICENSE"}, "@ntegral/nestjs-sentry@4.0.1": {"licenses": "ISC", "repository": "https://github.com/ntegral/nestjs-sentry", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ntegral/nestjs-sentry", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ntegral/nestjs-sentry/LICENSE.MD"}, "@nuxtjs/opencollective@0.3.2": {"licenses": "MIT", "repository": "https://github.com/nuxt-contrib/opencollective", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nuxtjs/opencollective", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nuxtjs/opencollective/LICENSE"}, "@octokit/auth-token@3.0.4": {"licenses": "MIT", "repository": "https://github.com/octokit/auth-token.js", "publisher": "<PERSON>", "url": "https://github.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/auth-token", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/auth-token/LICENSE"}, "@octokit/core@4.2.4": {"licenses": "MIT", "repository": "https://github.com/octokit/core.js", "publisher": "<PERSON>", "url": "https://github.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/core/LICENSE"}, "@octokit/endpoint@7.0.6": {"licenses": "MIT", "repository": "https://github.com/octokit/endpoint.js", "publisher": "<PERSON>", "url": "https://github.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/endpoint", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/endpoint/LICENSE"}, "@octokit/graphql@5.0.6": {"licenses": "MIT", "repository": "https://github.com/octokit/graphql.js", "publisher": "<PERSON>", "url": "https://github.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/graphql", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/graphql/LICENSE"}, "@octokit/openapi-types@18.1.1": {"licenses": "MIT", "repository": "https://github.com/octokit/openapi-types.ts", "publisher": "<PERSON>", "url": "https://twitter.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/openapi-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/openapi-types/LICENSE"}, "@octokit/plugin-paginate-rest@6.1.2": {"licenses": "MIT", "repository": "https://github.com/octokit/plugin-paginate-rest.js", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-paginate-rest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-paginate-rest/LICENSE"}, "@octokit/plugin-request-log@1.0.4": {"licenses": "MIT", "repository": "https://github.com/octokit/plugin-request-log.js", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-request-log", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-request-log/LICENSE"}, "@octokit/plugin-rest-endpoint-methods@7.2.3": {"licenses": "MIT", "repository": "https://github.com/octokit/plugin-rest-endpoint-methods.js", "publisher": "<PERSON>", "url": "https://twitter.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-rest-endpoint-methods", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-rest-endpoint-methods/LICENSE"}, "@octokit/request-error@3.0.3": {"licenses": "MIT", "repository": "https://github.com/octokit/request-error.js", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/request-error", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/request-error/LICENSE"}, "@octokit/request@6.2.8": {"licenses": "MIT", "repository": "https://github.com/octokit/request.js", "publisher": "<PERSON>", "url": "https://github.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/request", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/request/LICENSE"}, "@octokit/rest@19.0.13": {"licenses": "MIT", "repository": "https://github.com/octokit/rest.js", "publisher": "<PERSON>", "url": "https://github.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/rest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/rest/LICENSE"}, "@octokit/tsconfig@1.0.2": {"licenses": "MIT", "repository": "https://github.com/octokit/tsconfig", "publisher": "<PERSON>", "url": "https://dev.to/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/tsconfig", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/tsconfig/LICENSE"}, "@octokit/types@10.0.0": {"licenses": "MIT", "repository": "https://github.com/octokit/types.ts", "publisher": "<PERSON>", "url": "https://twitter.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-rest-endpoint-methods/node_modules/@octokit/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/plugin-rest-endpoint-methods/node_modules/@octokit/types/LICENSE"}, "@octokit/types@9.3.2": {"licenses": "MIT", "repository": "https://github.com/octokit/types.ts", "publisher": "<PERSON>", "url": "https://twitter.com/gr2m", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@octokit/types/LICENSE"}, "@pkgjs/parseargs@0.11.0": {"licenses": "MIT", "repository": "https://github.com/pkgjs/parseargs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pkgjs/parseargs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pkgjs/parseargs/LICENSE"}, "@pkgr/core@0.1.1": {"licenses": "MIT", "repository": "https://github.com/un-ts/pkgr", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.1stG.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pkgr/core"}, "@pnpm/config.env-replace@1.1.0": {"licenses": "MIT", "repository": "https://github.com/pnpm/components", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pnpm/config.env-replace"}, "@pnpm/network.ca-file@1.0.2": {"licenses": "MIT", "repository": "https://github.com/pnpm/components", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pnpm/network.ca-file"}, "@pnpm/npm-conf@2.3.1": {"licenses": "MIT", "repository": "https://github.com/pnpm/npm-conf", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pnpm/npm-conf", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pnpm/npm-conf/license"}, "@prisma/client@5.13.0": {"licenses": "Apache-2.0", "repository": "https://github.com/prisma/prisma", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/client", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/client/LICENSE"}, "@prisma/debug@5.13.0": {"licenses": "Apache-2.0", "repository": "https://github.com/prisma/prisma", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/debug", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/debug/LICENSE"}, "@prisma/engines-version@5.13.0-23.b9a39a7ee606c28e3455d0fd60e78c3ba82b1a2b": {"licenses": "Apache-2.0", "repository": "https://github.com/prisma/engines-wrapper", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/engines-version", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/engines-version/LICENSE"}, "@prisma/engines@5.13.0": {"licenses": "Apache-2.0", "repository": "https://github.com/prisma/prisma", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/engines", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/engines/LICENSE"}, "@prisma/fetch-engine@5.13.0": {"licenses": "Apache-2.0", "repository": "https://github.com/prisma/prisma", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/fetch-engine", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/fetch-engine/LICENSE"}, "@prisma/get-platform@5.13.0": {"licenses": "Apache-2.0", "repository": "https://github.com/prisma/prisma", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/get-platform", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@prisma/get-platform/LICENSE"}, "@sentry-internal/tracing@7.114.0": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing/LICENSE"}, "@sentry/core@7.114.0": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing/node_modules/@sentry/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing/node_modules/@sentry/core/LICENSE"}, "@sentry/core@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/core/LICENSE"}, "@sentry/hub@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/hub", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/hub/LICENSE"}, "@sentry/integrations@7.114.0": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/integrations", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/integrations/LICENSE"}, "@sentry/node@7.114.0": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/node", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/node/LICENSE"}, "@sentry/types@7.114.0": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing/node_modules/@sentry/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing/node_modules/@sentry/types/LICENSE"}, "@sentry/types@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/types/LICENSE"}, "@sentry/utils@7.114.0": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing/node_modules/@sentry/utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry-internal/tracing/node_modules/@sentry/utils/LICENSE"}, "@sentry/utils@7.120.3": {"licenses": "MIT", "repository": "https://github.com/getsentry/sentry-javascript", "publisher": "Sentry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sentry/utils/LICENSE"}, "@sinclair/typebox@0.27.8": {"licenses": "MIT", "repository": "https://github.com/sinclairzx81/typebox", "publisher": "sinclairzx81", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sinclair/typebox", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sinclair/typebox/license"}, "@sindresorhus/is@5.6.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sindresorhus/is", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sindresorhus/is/license"}, "@sinonjs/commons@3.0.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/sinonjs/commons", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sinonjs/commons", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sinonjs/commons/LICENSE"}, "@sinonjs/fake-timers@10.3.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/sinonjs/fake-timers", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sinonjs/fake-timers", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sinonjs/fake-timers/LICENSE"}, "@suites/core.unit@3.0.1": {"licenses": "Apache-2.0", "repository": "https://github.com/suites-dev/suites", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/core.unit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/core.unit/LICENSE"}, "@suites/di.nestjs@3.0.1": {"licenses": "Apache-2.0", "repository": "https://github.com/suites-dev/suites", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/di.nestjs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/di.nestjs/LICENSE"}, "@suites/doubles.jest@3.0.1": {"licenses": "Apache-2.0", "repository": "https://github.com/suites-dev/suites", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/doubles.jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/doubles.jest/LICENSE"}, "@suites/types.common@3.0.0": {"licenses": "Apache-2.0", "repository": "https://github.com/suites-dev/suites", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/types.common", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/types.common/LICENSE"}, "@suites/types.di@3.0.0": {"licenses": "Apache-2.0", "repository": "https://github.com/suites-dev/suites", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/types.di", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/types.di/LICENSE"}, "@suites/types.doubles@3.0.0": {"licenses": "Apache-2.0", "repository": "https://github.com/suites-dev/suites", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/types.doubles", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/types.doubles/LICENSE"}, "@suites/unit@3.0.1": {"licenses": "Apache-2.0", "repository": "https://github.com/suites-dev/suites", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/unit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@suites/unit/LICENSE"}, "@sw-web/nestjs-core@1.14.0": {"licenses": "UNKNOWN", "repository": "https://gitlab.uastage.com/sw-web/nestjs-core", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core"}, "@szmarczak/http-timer@5.0.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/http-timer", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@szmarczak/http-timer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@szmarczak/http-timer/LICENSE"}, "@tootallnate/quickjs-emscripten@0.23.0": {"licenses": "MIT", "repository": "https://github.com/justjake/quickjs-emscripten", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tootallnate/quickjs-emscripten", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tootallnate/quickjs-emscripten/LICENSE"}, "@tsconfig/node10@1.0.11": {"licenses": "MIT", "repository": "https://github.com/tsconfig/bases", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node10", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node10/LICENSE"}, "@tsconfig/node12@1.0.11": {"licenses": "MIT", "repository": "https://github.com/tsconfig/bases", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node12", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node12/LICENSE"}, "@tsconfig/node14@1.0.3": {"licenses": "MIT", "repository": "https://github.com/tsconfig/bases", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node14", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node14/LICENSE"}, "@tsconfig/node16@1.0.4": {"licenses": "MIT", "repository": "https://github.com/tsconfig/bases", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node16", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@tsconfig/node16/LICENSE"}, "@types/amqplib@0.10.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/amqplib", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/amqplib/LICENSE"}, "@types/babel__core@7.20.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__core/LICENSE"}, "@types/babel__generator@7.6.8": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__generator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__generator/LICENSE"}, "@types/babel__template@7.4.4": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__template", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__template/LICENSE"}, "@types/babel__traverse@7.20.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__traverse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/babel__traverse/LICENSE"}, "@types/body-parser@1.19.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/body-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/body-parser/LICENSE"}, "@types/connect@3.4.38": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/connect", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/connect/LICENSE"}, "@types/cookie-parser@1.4.7": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/cookie-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/cookie-parser/LICENSE"}, "@types/cookiejar@2.1.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/cookiejar", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/cookiejar/LICENSE"}, "@types/cron@2.4.0": {"licenses": "MIT", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/cron", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/cron/LICENSE"}, "@types/deep-diff@1.0.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/deep-diff", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/deep-diff/LICENSE"}, "@types/eslint-scope@3.7.7": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/eslint-scope", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/eslint-scope/LICENSE"}, "@types/eslint@8.56.10": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/eslint", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/eslint/LICENSE"}, "@types/estree@1.0.7": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/estree", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/estree/LICENSE"}, "@types/express-serve-static-core@4.19.0": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/express-serve-static-core", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/express-serve-static-core/LICENSE"}, "@types/express@4.17.21": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/express", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/express/LICENSE"}, "@types/graceful-fs@4.1.9": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/graceful-fs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/graceful-fs/LICENSE"}, "@types/http-cache-semantics@4.0.4": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/http-cache-semantics", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/http-cache-semantics/LICENSE"}, "@types/http-errors@2.0.4": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/http-errors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/http-errors/LICENSE"}, "@types/inquirer@8.2.10": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/inquirer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/inquirer/LICENSE"}, "@types/istanbul-lib-coverage@2.0.6": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/istanbul-lib-coverage", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/istanbul-lib-coverage/LICENSE"}, "@types/istanbul-lib-report@3.0.3": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/istanbul-lib-report", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/istanbul-lib-report/LICENSE"}, "@types/istanbul-reports@3.0.4": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/istanbul-reports", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/istanbul-reports/LICENSE"}, "@types/jest@29.5.12": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/jest/LICENSE"}, "@types/json-schema@7.0.15": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/json-schema", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/json-schema/LICENSE"}, "@types/json5@0.0.29": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "publisher": "<PERSON>", "email": "https://jasonswearingen.github.io", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/json5", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/json5/README.md"}, "@types/lodash@4.17.1": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/lodash", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/lodash/LICENSE"}, "@types/luxon@3.3.8": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/luxon", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/luxon/LICENSE"}, "@types/methods@1.1.4": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/methods", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/methods/LICENSE"}, "@types/mime@1.3.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/mime", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/mime/LICENSE"}, "@types/node@20.12.12": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/node", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/node/LICENSE"}, "@types/pg@8.11.10": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/pg", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/pg/LICENSE"}, "@types/qs@6.9.15": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/qs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/qs/LICENSE"}, "@types/range-parser@1.2.7": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/range-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/range-parser/LICENSE"}, "@types/semver@7.5.8": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/semver", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/semver/LICENSE"}, "@types/send@0.17.4": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/send", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/send/LICENSE"}, "@types/serve-static@1.15.7": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/serve-static", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/serve-static/LICENSE"}, "@types/stack-utils@2.0.3": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/stack-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/stack-utils/LICENSE"}, "@types/superagent@8.1.7": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/superagent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/superagent/LICENSE"}, "@types/supertest@2.0.16": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/supertest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/supertest/LICENSE"}, "@types/through@0.0.33": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/through", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/through/LICENSE"}, "@types/triple-beam@1.3.5": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/triple-beam", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/triple-beam/LICENSE"}, "@types/ua-parser-js@0.7.39": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/ua-parser-js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/ua-parser-js/LICENSE"}, "@types/uuid@9.0.8": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/uuid", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/uuid/LICENSE"}, "@types/validator@13.11.10": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/validator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/validator/LICENSE"}, "@types/yargs-parser@21.0.3": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/yargs-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/yargs-parser/LICENSE"}, "@types/yargs@17.0.32": {"licenses": "MIT", "repository": "https://github.com/DefinitelyTyped/DefinitelyTyped", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/yargs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@types/yargs/LICENSE"}, "@typescript-eslint/eslint-plugin@6.21.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/eslint-plugin", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/eslint-plugin/LICENSE"}, "@typescript-eslint/parser@6.21.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/parser/LICENSE"}, "@typescript-eslint/scope-manager@5.62.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/scope-manager", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/scope-manager/LICENSE"}, "@typescript-eslint/scope-manager@6.21.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/scope-manager", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/scope-manager/LICENSE"}, "@typescript-eslint/type-utils@6.21.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/type-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/type-utils/LICENSE"}, "@typescript-eslint/types@5.62.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/types/LICENSE"}, "@typescript-eslint/types@6.21.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/types/LICENSE"}, "@typescript-eslint/typescript-estree@5.62.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/typescript-estree", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/typescript-estree/LICENSE"}, "@typescript-eslint/typescript-estree@6.21.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/typescript-estree", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/typescript-estree/LICENSE"}, "@typescript-eslint/utils@5.62.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/utils/LICENSE"}, "@typescript-eslint/utils@6.21.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/utils/LICENSE"}, "@typescript-eslint/visitor-keys@5.62.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/visitor-keys", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/@typescript-eslint/visitor-keys/LICENSE"}, "@typescript-eslint/visitor-keys@6.21.0": {"licenses": "MIT", "repository": "https://github.com/typescript-eslint/typescript-eslint", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/visitor-keys", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@typescript-eslint/visitor-keys/LICENSE"}, "@ungap/structured-clone@1.2.0": {"licenses": "ISC", "repository": "https://github.com/ungap/structured-clone", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ungap/structured-clone", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@ungap/structured-clone/LICENSE"}, "@webassemblyjs/ast@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/ast", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/ast/LICENSE"}, "@webassemblyjs/floating-point-hex-parser@1.13.2": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/floating-point-hex-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/floating-point-hex-parser/LICENSE"}, "@webassemblyjs/helper-api-error@1.13.2": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-api-error", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-api-error/LICENSE"}, "@webassemblyjs/helper-buffer@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-buffer/LICENSE"}, "@webassemblyjs/helper-numbers@1.13.2": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-numbers", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-numbers/LICENSE"}, "@webassemblyjs/helper-wasm-bytecode@1.13.2": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-wasm-bytecode", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-wasm-bytecode/LICENSE"}, "@webassemblyjs/helper-wasm-section@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-wasm-section", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/helper-wasm-section/LICENSE"}, "@webassemblyjs/ieee754@1.13.2": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/ieee754", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/ieee754/LICENSE"}, "@webassemblyjs/leb128@1.13.2": {"licenses": "Apache-2.0", "repository": "https://github.com/xtuc/webassemblyjs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/leb128", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/leb128/LICENSE.txt"}, "@webassemblyjs/utf8@1.13.2": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/utf8", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/utf8/LICENSE"}, "@webassemblyjs/wasm-edit@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-edit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-edit/LICENSE"}, "@webassemblyjs/wasm-gen@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-gen", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-gen/LICENSE"}, "@webassemblyjs/wasm-opt@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-opt", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-opt/LICENSE"}, "@webassemblyjs/wasm-parser@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wasm-parser/LICENSE"}, "@webassemblyjs/wast-printer@1.14.1": {"licenses": "MIT", "repository": "https://github.com/xtuc/webassemblyjs", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wast-printer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@webassemblyjs/wast-printer/LICENSE"}, "@xtuc/ieee754@1.2.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/feross/ieee754", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@xtuc/ieee754", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@xtuc/ieee754/LICENSE"}, "@xtuc/long@4.2.2": {"licenses": "Apache-2.0", "repository": "https://github.com/dcodeIO/long.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@xtuc/long", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@xtuc/long/LICENSE"}, "accepts@1.3.8": {"licenses": "MIT", "repository": "https://github.com/jshttp/accepts", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/accepts", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/accepts/LICENSE"}, "acorn-import-assertions@1.9.0": {"licenses": "MIT", "repository": "https://github.com/xtuc/acorn-import-assertions", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn-import-assertions", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn-import-assertions/README.md"}, "acorn-jsx@5.3.2": {"licenses": "MIT", "repository": "https://github.com/acornjs/acorn-jsx", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn-jsx", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn-jsx/LICENSE"}, "acorn-walk@8.3.2": {"licenses": "MIT", "repository": "https://github.com/acornjs/acorn", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn-walk", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn-walk/LICENSE"}, "acorn@8.14.1": {"licenses": "MIT", "repository": "https://github.com/acornjs/acorn", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/acorn/LICENSE"}, "agent-base@7.1.3": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/agent-base", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/agent-base/LICENSE"}, "ajv-formats@2.1.1": {"licenses": "MIT", "repository": "https://github.com/ajv-validator/ajv-formats", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ajv-formats", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ajv-formats/LICENSE"}, "ajv-keywords@3.5.2": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/ajv-keywords", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils/node_modules/ajv-keywords", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils/node_modules/ajv-keywords/LICENSE"}, "ajv-keywords@5.1.0": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/ajv-keywords", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ajv-keywords", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ajv-keywords/LICENSE"}, "ajv@6.12.6": {"licenses": "MIT", "repository": "https://github.com/ajv-validator/ajv", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils/node_modules/ajv", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils/node_modules/ajv/LICENSE"}, "ajv@8.12.0": {"licenses": "MIT", "repository": "https://github.com/ajv-validator/ajv", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ajv", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ajv/LICENSE"}, "amqp-connection-manager@4.1.14": {"licenses": "MIT", "repository": "https://github.com/jwalton/node-amqp-connection-manager", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/jwalton", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/amqp-connection-manager", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/amqp-connection-manager/README.md"}, "amqplib@0.10.4": {"licenses": "MIT", "repository": "https://github.com/amqp-node/amqplib", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/amqplib", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/amqplib/LICENSE"}, "ansi-align@3.0.1": {"licenses": "ISC", "repository": "https://github.com/nexdrew/ansi-align", "publisher": "nexdrew", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-align", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-align/LICENSE"}, "ansi-colors@4.1.3": {"licenses": "MIT", "repository": "https://github.com/doowb/ansi-colors", "publisher": "<PERSON>", "url": "https://github.com/doowb", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-colors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-colors/LICENSE"}, "ansi-escapes@4.3.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/ansi-escapes", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-escapes", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-escapes/license"}, "ansi-regex@5.0.1": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-regex", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-regex/license"}, "ansi-regex@6.0.1": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-regex", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ansi-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ansi-regex/license"}, "ansi-styles@3.2.1": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-styles", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/ansi-styles", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/ansi-styles/license"}, "ansi-styles@4.3.0": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-styles", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-styles", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-styles/license"}, "ansi-styles@5.2.0": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-styles", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pretty-format/node_modules/ansi-styles", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pretty-format/node_modules/ansi-styles/license"}, "ansi-styles@6.2.1": {"licenses": "MIT", "repository": "https://github.com/chalk/ansi-styles", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/ansi-styles", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/ansi-styles/license"}, "anymatch@3.1.3": {"licenses": "ISC", "repository": "https://github.com/micromatch/anymatch", "publisher": "<PERSON><PERSON>", "url": "https://github.com/es128", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/anymatch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/anymatch/LICENSE"}, "append-field@1.0.0": {"licenses": "MIT", "repository": "https://github.com/LinusU/node-append-field", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/append-field", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/append-field/LICENSE"}, "arg@4.1.3": {"licenses": "MIT", "repository": "https://github.com/zeit/arg", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/arg", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/arg/LICENSE.md"}, "argparse@1.0.10": {"licenses": "MIT", "repository": "https://github.com/nodeca/argparse", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/argparse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/argparse/LICENSE"}, "argparse@2.0.1": {"licenses": "Python-2.0", "repository": "https://github.com/nodeca/argparse", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/argparse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/argparse/LICENSE"}, "array-buffer-byte-length@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/array-buffer-byte-length", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-buffer-byte-length", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-buffer-byte-length/LICENSE"}, "array-flatten@1.1.1": {"licenses": "MIT", "repository": "https://github.com/blakeembrey/array-flatten", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-flatten", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-flatten/LICENSE"}, "array-includes@3.1.8": {"licenses": "MIT", "repository": "https://github.com/es-shims/array-includes", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-includes", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-includes/LICENSE"}, "array-timsort@1.0.3": {"licenses": "MIT", "repository": "https://github.com/kaelzhang/node-array-timsort", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-timsort", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-timsort/LICENSE"}, "array-union@2.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/array-union", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-union", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array-union/license"}, "array.prototype.findlastindex@1.2.5": {"licenses": "MIT", "repository": "https://github.com/es-shims/Array.prototype.findLastIndex", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.findlastindex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.findlastindex/LICENSE"}, "array.prototype.flat@1.3.2": {"licenses": "MIT", "repository": "https://github.com/es-shims/Array.prototype.flat", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.flat", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.flat/LICENSE"}, "array.prototype.flatmap@1.3.2": {"licenses": "MIT", "repository": "https://github.com/es-shims/Array.prototype.flatMap", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.flatmap", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.flatmap/LICENSE"}, "array.prototype.map@1.0.8": {"licenses": "MIT", "repository": "https://github.com/es-shims/Array.prototype.map", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/array.prototype.map/LICENSE"}, "arraybuffer.prototype.slice@1.0.4": {"licenses": "MIT", "repository": "https://github.com/es-shims/ArrayBuffer.prototype.slice", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/arraybuffer.prototype.slice", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/arraybuffer.prototype.slice/LICENSE"}, "asap@2.0.6": {"licenses": "MIT", "repository": "https://github.com/kriskowal/asap", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/asap", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/asap/LICENSE.md"}, "ast-types@0.13.4": {"licenses": "MIT", "repository": "https://github.com/benjamn/ast-types", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ast-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ast-types/LICENSE"}, "async-retry@1.3.3": {"licenses": "MIT", "repository": "https://github.com/vercel/async-retry", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/async-retry", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/async-retry/LICENSE.md"}, "async@3.2.5": {"licenses": "MIT", "repository": "https://github.com/caolan/async", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/async", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/async/LICENSE"}, "asynckit@0.4.0": {"licenses": "MIT", "repository": "https://github.com/alexindigo/asynckit", "publisher": "Alex Indigo", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/asynckit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/asynckit/LICENSE"}, "available-typed-arrays@1.0.7": {"licenses": "MIT", "repository": "https://github.com/inspect-js/available-typed-arrays", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/available-typed-arrays", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/available-typed-arrays/LICENSE"}, "axios@1.7.2": {"licenses": "MIT", "repository": "https://github.com/axios/axios", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/axios", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/axios/LICENSE"}, "babel-jest@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-jest/LICENSE"}, "babel-plugin-istanbul@6.1.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/istanbuljs/babel-plugin-istanbul", "publisher": "Thai Pangsakulyanont @dtinth", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-plugin-istanbul", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-plugin-istanbul/LICENSE"}, "babel-plugin-jest-hoist@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-plugin-jest-hoist", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-plugin-jest-hoist/LICENSE"}, "babel-preset-current-node-syntax@1.0.1": {"licenses": "MIT", "repository": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax", "publisher": "<PERSON><PERSON><PERSON>", "url": "https://github.com/nicolo-ribaudo", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-preset-current-node-syntax", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-preset-current-node-syntax/LICENSE"}, "babel-preset-jest@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-preset-jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-preset-jest/LICENSE"}, "backo2@1.0.2": {"licenses": "MIT", "repository": "https://github.com/mokesmokes/backo", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/backo2", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/backo2/Readme.md"}, "balanced-match@1.0.2": {"licenses": "MIT", "repository": "https://github.com/juliangruber/balanced-match", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/balanced-match", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/balanced-match/LICENSE.md"}, "base64-js@1.5.1": {"licenses": "MIT", "repository": "https://github.com/beatgammit/base64-js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/base64-js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/base64-js/LICENSE"}, "basic-ftp@5.0.5": {"licenses": "MIT", "repository": "https://github.com/patrickjuchli/basic-ftp", "publisher": "<PERSON>", "email": "pat<PERSON><PERSON><PERSON><PERSON>@gmail.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/basic-ftp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/basic-ftp/LICENSE.txt"}, "before-after-hook@2.2.3": {"licenses": "Apache-2.0", "repository": "https://github.com/gr2m/before-after-hook", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/before-after-hook", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/before-after-hook/LICENSE"}, "big-integer@1.6.52": {"licenses": "Unlicense", "repository": "https://github.com/peterolson/BigInteger.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/big-integer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/big-integer/LICENSE"}, "binary-extensions@2.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/binary-extensions", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/binary-extensions", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/binary-extensions/license"}, "bl@4.1.0": {"licenses": "MIT", "repository": "https://github.com/rvagg/bl", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bl", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bl/LICENSE.md"}, "bl@5.1.0": {"licenses": "MIT", "repository": "https://github.com/rvagg/bl", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stdin-discarder/node_modules/bl", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stdin-discarder/node_modules/bl/LICENSE.md"}, "body-parser@1.20.2": {"licenses": "MIT", "repository": "https://github.com/expressjs/body-parser", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/body-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/body-parser/LICENSE"}, "body-parser@1.20.3": {"licenses": "MIT", "repository": "https://github.com/expressjs/body-parser", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/body-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/body-parser/LICENSE"}, "boxen@5.1.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/boxen", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/boxen", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/boxen/license"}, "boxen@7.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/boxen", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/boxen", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/boxen/license"}, "bplist-parser@0.2.0": {"licenses": "MIT", "repository": "https://github.com/nearinfinity/node-bplist-parser", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bplist-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bplist-parser/README.md"}, "brace-expansion@1.1.11": {"licenses": "MIT", "repository": "https://github.com/juliangruber/brace-expansion", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs/node_modules/brace-expansion", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs/node_modules/brace-expansion/LICENSE"}, "brace-expansion@2.0.1": {"licenses": "MIT", "repository": "https://github.com/juliangruber/brace-expansion", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/brace-expansion", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/brace-expansion/LICENSE"}, "braces@3.0.2": {"licenses": "MIT", "repository": "https://github.com/micromatch/braces", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/braces", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/braces/LICENSE"}, "browserslist@4.24.4": {"licenses": "MIT", "repository": "https://github.com/browserslist/browserslist", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/browserslist", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/browserslist/LICENSE"}, "bs-logger@0.2.6": {"licenses": "MIT", "repository": "https://github.com/huafu/bs-logger", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bs-logger", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bs-logger/LICENSE"}, "bser@2.1.1": {"licenses": "Apache-2.0", "repository": "https://github.com/facebook/watchman", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bser/README.md"}, "buffer-from@1.1.2": {"licenses": "MIT", "repository": "https://github.com/LinusU/buffer-from", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/buffer-from", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/buffer-from/LICENSE"}, "buffer-more-ints@1.0.0": {"licenses": "MIT", "repository": "https://github.com/dpw/node-buffer-more-ints", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/buffer-more-ints", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/buffer-more-ints/LICENSE"}, "buffer@5.7.1": {"licenses": "MIT", "repository": "https://github.com/feross/buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/buffer/LICENSE"}, "buffer@6.0.3": {"licenses": "MIT", "repository": "https://github.com/feross/buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stdin-discarder/node_modules/buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stdin-discarder/node_modules/buffer/LICENSE"}, "bundle-name@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/bundle-name", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bundle-name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bundle-name/license"}, "busboy@1.6.0": {"licenses": "MIT", "repository": "https://github.com/mscdex/busboy", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/busboy", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/busboy/LICENSE"}, "bytes@3.1.2": {"licenses": "MIT", "repository": "https://github.com/visionmedia/bytes.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bytes", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bytes/LICENSE"}, "cacheable-lookup@7.0.0": {"licenses": "MIT", "repository": "https://github.com/szmarczak/cacheable-lookup", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cacheable-lookup", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cacheable-lookup/LICENSE"}, "cacheable-request@10.2.14": {"licenses": "MIT", "repository": "https://github.com/jaredwray/cacheable", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cacheable-request", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cacheable-request/README.md"}, "call-bind-apply-helpers@1.0.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/call-bind-apply-helpers", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/call-bind-apply-helpers", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/call-bind-apply-helpers/LICENSE"}, "call-bind@1.0.8": {"licenses": "MIT", "repository": "https://github.com/ljharb/call-bind", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/call-bind", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/call-bind/LICENSE"}, "call-bound@1.0.3": {"licenses": "MIT", "repository": "https://github.com/ljharb/call-bound", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/call-bound", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/call-bound/LICENSE"}, "callsites@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/callsites", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/callsites", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/callsites/license"}, "camelcase@5.3.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/camelcase", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/camelcase/license"}, "camelcase@6.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/camelcase", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/camelcase", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/camelcase/license"}, "camelcase@7.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/camelcase", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/camelcase", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/camelcase/license"}, "caniuse-lite@1.0.30001715": {"licenses": "CC-BY-4.0", "repository": "https://github.com/browserslist/caniuse-lite", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/caniuse-lite", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/caniuse-lite/LICENSE"}, "chalk@2.4.2": {"licenses": "MIT", "repository": "https://github.com/chalk/chalk", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/chalk", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/chalk/license"}, "chalk@4.1.2": {"licenses": "MIT", "repository": "https://github.com/chalk/chalk", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chalk", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chalk/license"}, "chalk@5.3.0": {"licenses": "MIT", "repository": "https://github.com/chalk/chalk", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/chalk", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/chalk/license"}, "chalk@5.4.1": {"licenses": "MIT", "repository": "https://github.com/chalk/chalk", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/chalk", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/chalk/license"}, "char-regex@1.0.2": {"licenses": "MIT", "repository": "https://github.com/Richienb/char-regex", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/char-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/char-regex/LICENSE"}, "chardet@0.7.0": {"licenses": "MIT", "repository": "https://github.com/runk/node-chardet", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chardet", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chardet/LICENSE"}, "check-disk-space@3.4.0": {"licenses": "MIT", "repository": "https://github.com/Alex-D/check-disk-space", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://alex-d.fr", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/check-disk-space", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/check-disk-space/LICENSE"}, "chokidar@3.5.3": {"licenses": "MIT", "repository": "https://github.com/paulmillr/chokidar", "publisher": "<PERSON>", "url": "https://paulmillr.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/graphql/node_modules/chokidar", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/graphql/node_modules/chokidar/LICENSE"}, "chokidar@3.6.0": {"licenses": "MIT", "repository": "https://github.com/paulmillr/chokidar", "publisher": "<PERSON>", "url": "https://paulmillr.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chokidar", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chokidar/LICENSE"}, "chrome-trace-event@1.0.3": {"licenses": "MIT", "repository": "https://github.com/samccone/chrome-trace-event", "publisher": "<PERSON>, <PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chrome-trace-event", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/chrome-trace-event/LICENSE.txt"}, "ci-info@3.9.0": {"licenses": "MIT", "repository": "https://github.com/watson/ci-info", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ci-info", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ci-info/LICENSE"}, "cjs-module-lexer@1.3.1": {"licenses": "MIT", "repository": "https://github.com/nodejs/cjs-module-lexer", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cjs-module-lexer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cjs-module-lexer/LICENSE"}, "class-transformer@0.5.1": {"licenses": "MIT", "repository": "https://github.com/typestack/class-transformer", "publisher": "TypeStack contributors", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/class-transformer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/class-transformer/LICENSE"}, "class-validator@0.14.1": {"licenses": "MIT", "repository": "https://github.com/typestack/class-validator", "publisher": "TypeStack contributors", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/class-validator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/class-validator/LICENSE"}, "clean-architecture-nest@0.0.1": {"licenses": "UNLICENSED", "private": true, "path": "/Users/<USER>/Documents/itrdev/sales-hub", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/license-report.js"}, "cli-boxes@2.2.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-boxes", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-boxes", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-boxes/license"}, "cli-boxes@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-boxes", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/cli-boxes", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/cli-boxes/license"}, "cli-cursor@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-cursor", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-cursor/license"}, "cli-cursor@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/cli-cursor", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/cli-cursor/license"}, "cli-spinners@2.9.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/cli-spinners", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-spinners", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-spinners/license"}, "cli-table3@0.6.3": {"licenses": "MIT", "repository": "https://github.com/cli-table/cli-table3", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-table3", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-table3/LICENSE"}, "cli-width@3.0.0": {"licenses": "ISC", "repository": "https://github.com/knownasilya/cli-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-width", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cli-width/LICENSE"}, "cli-width@4.1.0": {"licenses": "ISC", "repository": "https://github.com/knownasilya/cli-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/cli-width", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/cli-width/LICENSE"}, "cliui@8.0.1": {"licenses": "ISC", "repository": "https://github.com/yargs/cliui", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cliui", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cliui/LICENSE.txt"}, "clone@1.0.4": {"licenses": "MIT", "repository": "https://github.com/pvorb/node-clone", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://paul.vorba.ch/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/clone", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/clone/LICENSE"}, "co@4.6.0": {"licenses": "MIT", "repository": "https://github.com/tj/co", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/co", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/co/LICENSE"}, "collect-v8-coverage@1.0.2": {"licenses": "MIT", "repository": "https://github.com/SimenB/collect-v8-coverage", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/collect-v8-coverage", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/collect-v8-coverage/LICENSE"}, "color-convert@1.9.3": {"licenses": "MIT", "repository": "https://github.com/Qix-/color-convert", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/color-convert", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/color-convert/LICENSE"}, "color-convert@2.0.1": {"licenses": "MIT", "repository": "https://github.com/Qix-/color-convert", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color-convert", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color-convert/LICENSE"}, "color-name@1.1.3": {"licenses": "MIT", "repository": "https://github.com/dfcreative/color-name", "publisher": "DY", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/color-name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/color-name/LICENSE"}, "color-name@1.1.4": {"licenses": "MIT", "repository": "https://github.com/colorjs/color-name", "publisher": "DY", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color-name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color-name/LICENSE"}, "color-string@1.9.1": {"licenses": "MIT", "repository": "https://github.com/Qix-/color-string", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color-string/LICENSE"}, "color@3.2.1": {"licenses": "MIT", "repository": "https://github.com/Qix-/color", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/color/LICENSE"}, "colorspace@1.1.4": {"licenses": "MIT", "repository": "https://github.com/3rd-Eden/colorspace", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/colorspace", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/colorspace/LICENSE.md"}, "combined-stream@1.0.8": {"licenses": "MIT", "repository": "https://github.com/felixge/node-combined-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/combined-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/combined-stream/License"}, "commander@11.1.0": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/nest-commander/node_modules/commander", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/nest-commander/node_modules/commander/LICENSE"}, "commander@2.20.3": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser/node_modules/commander", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser/node_modules/commander/LICENSE"}, "commander@4.1.1": {"licenses": "MIT", "repository": "https://github.com/tj/commander.js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/commander", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/commander/LICENSE"}, "comment-json@4.2.3": {"licenses": "MIT", "repository": "https://github.com/kaelzhang/node-comment-json", "publisher": "ka<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/comment-json", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/comment-json/LICENSE"}, "component-emitter@1.3.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/component-emitter", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/component-emitter", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/component-emitter/LICENSE"}, "concat-map@0.0.1": {"licenses": "MIT", "repository": "https://github.com/substack/node-concat-map", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-map/LICENSE"}, "concat-stream@1.6.2": {"licenses": "MIT", "repository": "https://github.com/maxogden/concat-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream/LICENSE"}, "config-chain@1.1.13": {"licenses": "MIT", "repository": "https://github.com/dominictarr/config-chain", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/config-chain", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/config-chain/LICENCE"}, "configstore@6.0.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/yeoman/configstore", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/configstore", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/configstore/license"}, "confusing-browser-globals@1.0.11": {"licenses": "MIT", "repository": "https://github.com/facebook/create-react-app", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/confusing-browser-globals", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/confusing-browser-globals/LICENSE"}, "consola@2.15.3": {"licenses": "MIT", "repository": "https://github.com/nuxt/consola", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/consola", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/consola/README.md"}, "content-disposition@0.5.4": {"licenses": "MIT", "repository": "https://github.com/jshttp/content-disposition", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/content-disposition", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/content-disposition/LICENSE"}, "content-type@1.0.5": {"licenses": "MIT", "repository": "https://github.com/jshttp/content-type", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/content-type", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/content-type/LICENSE"}, "convert-source-map@2.0.0": {"licenses": "MIT", "repository": "https://github.com/thlorenz/convert-source-map", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/convert-source-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/convert-source-map/LICENSE"}, "cookie-parser@1.4.6": {"licenses": "MIT", "repository": "https://github.com/expressjs/cookie-parser", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookie-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookie-parser/LICENSE"}, "cookie-signature@1.0.6": {"licenses": "MIT", "repository": "https://github.com/visionmedia/node-cookie-signature", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookie-signature", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookie-signature/Readme.md"}, "cookie@0.4.1": {"licenses": "MIT", "repository": "https://github.com/jshttp/cookie", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookie", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookie/LICENSE"}, "cookie@0.6.0": {"licenses": "MIT", "repository": "https://github.com/jshttp/cookie", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/express/node_modules/cookie", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/express/node_modules/cookie/LICENSE"}, "cookie@0.7.1": {"licenses": "MIT", "repository": "https://github.com/jshttp/cookie", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/cookie", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/cookie/LICENSE"}, "cookiejar@2.1.4": {"licenses": "MIT", "repository": "https://github.com/bmeck/node-cookiejar", "publisher": "bradleymeck", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookiejar", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cookiejar/LICENSE"}, "core-util-is@1.0.3": {"licenses": "MIT", "repository": "https://github.com/isaacs/core-util-is", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/core-util-is", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/core-util-is/LICENSE"}, "cors@2.8.5": {"licenses": "MIT", "repository": "https://github.com/expressjs/cors", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/troygoode/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cors/LICENSE"}, "cosmiconfig@8.3.6": {"licenses": "MIT", "repository": "https://github.com/cosmiconfig/cosmiconfig", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cosmiconfig", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cosmiconfig/LICENSE"}, "create-jest@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/create-jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/create-jest/LICENSE"}, "create-require@1.1.1": {"licenses": "MIT", "repository": "https://github.com/nuxt-contrib/create-require", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/create-require", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/create-require/LICENSE"}, "cron@2.4.3": {"licenses": "MIT", "repository": "https://github.com/kelektiv/node-cron", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/ncb000gt", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cron", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cron/LICENSE"}, "cross-env@7.0.3": {"licenses": "MIT", "repository": "https://github.com/kentcdodds/cross-env", "publisher": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cross-env", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cross-env/LICENSE"}, "cross-inspect@1.0.0": {"licenses": "MIT", "repository": "https://github.com/ardatan/graphql-tools", "publisher": "Arda TANRIKULU", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cross-inspect"}, "cross-spawn@7.0.3": {"licenses": "MIT", "repository": "https://github.com/moxystudio/node-cross-spawn", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cross-spawn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/cross-spawn/LICENSE"}, "crypto-random-string@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/crypto-random-string", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/crypto-random-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/crypto-random-string/license"}, "crypto@1.0.1": {"licenses": "ISC", "repository": "https://github.com/npm/deprecate-holder", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/crypto", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/crypto/README.md"}, "data-uri-to-buffer@4.0.1": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/node-data-uri-to-buffer", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/data-uri-to-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/data-uri-to-buffer/README.md"}, "data-uri-to-buffer@6.0.2": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-uri-to-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-uri-to-buffer/LICENSE"}, "data-view-buffer@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/data-view-buffer", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-view-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-view-buffer/LICENSE"}, "data-view-byte-length@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/data-view-byte-length", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-view-byte-length", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-view-byte-length/LICENSE"}, "data-view-byte-offset@1.0.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/data-view-byte-offset", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-view-byte-offset", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/data-view-byte-offset/LICENSE"}, "dayjs@1.11.11": {"licenses": "MIT", "repository": "https://github.com/iamkun/dayjs", "publisher": "i<PERSON><PERSON>n", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dayjs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dayjs/LICENSE"}, "debug@2.6.9": {"licenses": "MIT", "repository": "https://github.com/visionmedia/debug", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/body-parser/node_modules/debug", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/body-parser/node_modules/debug/LICENSE"}, "debug@3.2.7": {"licenses": "MIT", "repository": "https://github.com/visionmedia/debug", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-module-utils/node_modules/debug", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-module-utils/node_modules/debug/LICENSE"}, "debug@4.3.4": {"licenses": "MIT", "repository": "https://github.com/debug-js/debug", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/debug", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/debug/LICENSE"}, "decimal.js@10.4.3": {"licenses": "MIT", "repository": "https://github.com/MikeMcl/decimal.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/decimal.js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/decimal.js/LICENCE.md"}, "decompress-response@6.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/decompress-response", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/decompress-response", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/decompress-response/license"}, "dedent@1.5.3": {"licenses": "MIT", "repository": "https://github.com/dmnd/dedent", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://desmondbrand.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dedent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dedent/LICENSE.md"}, "deep-diff@1.0.2": {"licenses": "MIT", "repository": "https://github.com/flitbit/diff", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deep-diff", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deep-diff/LICENSE"}, "deep-extend@0.6.0": {"licenses": "MIT", "repository": "https://github.com/unclechu/node-deep-extend", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deep-extend", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deep-extend/LICENSE"}, "deep-is@0.1.4": {"licenses": "MIT", "repository": "https://github.com/thlorenz/deep-is", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deep-is", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deep-is/LICENSE"}, "deepmerge@4.3.1": {"licenses": "MIT", "repository": "https://github.com/TehShrike/deepmerge", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deepmerge", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deepmerge/license.txt"}, "default-browser-id@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/default-browser-id", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/default-browser-id", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/default-browser-id/license"}, "default-browser@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/default-browser", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/default-browser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/default-browser/license"}, "defaults@1.0.4": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/node-defaults", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/defaults", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/defaults/LICENSE"}, "defer-to-connect@2.0.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/defer-to-connect", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/defer-to-connect", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/defer-to-connect/LICENSE"}, "define-data-property@1.1.4": {"licenses": "MIT", "repository": "https://github.com/ljharb/define-data-property", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/define-data-property", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/define-data-property/LICENSE"}, "define-lazy-prop@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/define-lazy-prop", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/define-lazy-prop", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/define-lazy-prop/license"}, "define-properties@1.2.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/define-properties", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/define-properties", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/define-properties/LICENSE"}, "degenerator@5.0.1": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/degenerator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/degenerator/README.md"}, "delayed-stream@1.0.0": {"licenses": "MIT", "repository": "https://github.com/felixge/node-delayed-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/delayed-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/delayed-stream/License"}, "depd@2.0.0": {"licenses": "MIT", "repository": "https://github.com/dougwilson/nodejs-depd", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/depd", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/depd/LICENSE"}, "deprecation@2.3.1": {"licenses": "ISC", "repository": "https://github.com/gr2m/deprecation", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deprecation", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/deprecation/LICENSE"}, "destroy@1.2.0": {"licenses": "MIT", "repository": "https://github.com/stream-utils/destroy", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/destroy", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/destroy/LICENSE"}, "detect-newline@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/detect-newline", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/detect-newline", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/detect-newline/license"}, "dezalgo@1.0.4": {"licenses": "ISC", "repository": "https://github.com/npm/dezalgo", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dezalgo", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dezalgo/LICENSE"}, "diff-sequences@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/diff-sequences", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/diff-sequences/LICENSE"}, "diff@4.0.2": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/kpdecker/jsdiff", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/diff", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/diff/LICENSE"}, "dir-glob@3.0.1": {"licenses": "MIT", "repository": "https://github.com/kevva/dir-glob", "publisher": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dir-glob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dir-glob/license"}, "doctrine@2.1.0": {"licenses": "Apache-2.0", "repository": "https://github.com/eslint/doctrine", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import/node_modules/doctrine", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import/node_modules/doctrine/LICENSE"}, "doctrine@3.0.0": {"licenses": "Apache-2.0", "repository": "https://github.com/eslint/doctrine", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/doctrine", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/doctrine/LICENSE"}, "dot-prop@6.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/dot-prop", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dot-prop", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dot-prop/license"}, "dset@3.1.3": {"licenses": "MIT", "repository": "https://github.com/lukeed/dset", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dset", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dset/license"}, "dunder-proto@1.0.1": {"licenses": "MIT", "repository": "https://github.com/es-shims/dunder-proto", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dunder-proto", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/dunder-proto/LICENSE"}, "eastasianwidth@0.2.0": {"licenses": "MIT", "repository": "https://github.com/komagata/eastasianwidth", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eastasianwidth", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eastasianwidth/README.md"}, "ee-first@1.1.1": {"licenses": "MIT", "repository": "https://github.com/jonathanong/ee-first", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ee-first", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ee-first/LICENSE"}, "electron-to-chromium@1.5.139": {"licenses": "ISC", "repository": "https://github.com/kilian/electron-to-chromium", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/electron-to-chromium", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/electron-to-chromium/LICENSE"}, "emittery@0.13.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/emittery", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/emittery", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/emittery/license"}, "emoji-regex@10.4.0": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/emoji-regex", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/emoji-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/emoji-regex/LICENSE-MIT.txt"}, "emoji-regex@8.0.0": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/emoji-regex", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/emoji-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/emoji-regex/LICENSE-MIT.txt"}, "emoji-regex@9.2.2": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/emoji-regex", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/emoji-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/emoji-regex/LICENSE-MIT.txt"}, "enabled@2.0.0": {"licenses": "MIT", "repository": "https://github.com/3rd-Eden/enabled", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/enabled", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/enabled/LICENSE"}, "encodeurl@1.0.2": {"licenses": "MIT", "repository": "https://github.com/pillarjs/encodeurl", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/encodeurl", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/encodeurl/LICENSE"}, "encodeurl@2.0.0": {"licenses": "MIT", "repository": "https://github.com/pillarjs/encodeurl", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/encodeurl", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/encodeurl/LICENSE"}, "enhanced-resolve@5.18.1": {"licenses": "MIT", "repository": "https://github.com/webpack/enhanced-resolve", "publisher": "<PERSON> @sokra", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/enhanced-resolve", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/enhanced-resolve/LICENSE"}, "env-cmd@10.1.0": {"licenses": "MIT", "repository": "https://github.com/toddbluhm/env-cmd", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/env-cmd", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/env-cmd/LICENSE"}, "env-var@7.4.2": {"licenses": "MIT", "repository": "https://github.com/evanshortiss/env-var", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/env-var", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/env-var/LICENSE"}, "error-ex@1.3.2": {"licenses": "MIT", "repository": "https://github.com/qix-/node-error-ex", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/error-ex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/error-ex/LICENSE"}, "es-abstract@1.23.9": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-abstract", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-abstract", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-abstract/LICENSE"}, "es-array-method-boxes-properly@1.0.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-array-method-boxes-properly", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-array-method-boxes-properly", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-array-method-boxes-properly/LICENSE"}, "es-define-property@1.0.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-define-property", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-define-property", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-define-property/LICENSE"}, "es-errors@1.3.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-errors", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-errors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-errors/LICENSE"}, "es-get-iterator@1.1.3": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-get-iterator", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-get-iterator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-get-iterator/LICENSE"}, "es-module-lexer@1.5.2": {"licenses": "MIT", "repository": "https://github.com/guybedford/es-module-lexer", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-module-lexer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-module-lexer/LICENSE"}, "es-object-atoms@1.0.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-object-atoms", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-object-atoms", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-object-atoms/LICENSE"}, "es-set-tostringtag@2.1.0": {"licenses": "MIT", "repository": "https://github.com/es-shims/es-set-tostringtag", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-set-tostringtag", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-set-tostringtag/LICENSE"}, "es-shim-unscopables@1.0.2": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-shim-unscopables", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-shim-unscopables", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-shim-unscopables/LICENSE"}, "es-to-primitive@1.3.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/es-to-primitive", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-to-primitive", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/es-to-primitive/LICENSE"}, "escalade@3.2.0": {"licenses": "MIT", "repository": "https://github.com/lukeed/escalade", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escalade", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escalade/license"}, "escape-goat@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-goat", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escape-goat", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escape-goat/license"}, "escape-html@1.0.3": {"licenses": "MIT", "repository": "https://github.com/component/escape-html", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escape-html", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escape-html/LICENSE"}, "escape-string-regexp@1.0.5": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-string-regexp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/escape-string-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/escape-string-regexp/license"}, "escape-string-regexp@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-string-regexp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stack-utils/node_modules/escape-string-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stack-utils/node_modules/escape-string-regexp/license"}, "escape-string-regexp@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-string-regexp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escape-string-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escape-string-regexp/license"}, "escape-string-regexp@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/escape-string-regexp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/escape-string-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/escape-string-regexp/license"}, "escodegen@2.1.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/escodegen", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escodegen", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escodegen/LICENSE.BSD"}, "eslint-config-airbnb-base@15.0.0": {"licenses": "MIT", "repository": "https://github.com/airbnb/javascript", "publisher": "<PERSON>", "url": "https://twitter.com/@jitl", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-config-airbnb-base", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-config-airbnb-base/LICENSE.md"}, "eslint-config-airbnb-typescript@17.1.0": {"licenses": "MIT", "repository": "https://github.com/iamturns/eslint-config-airbnb-typescript", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://iamturns.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-config-airbnb-typescript", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-config-airbnb-typescript/LICENSE"}, "eslint-config-prettier@9.1.0": {"licenses": "MIT", "repository": "https://github.com/prettier/eslint-config-prettier", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-config-prettier", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-config-prettier/LICENSE"}, "eslint-import-resolver-node@0.3.9": {"licenses": "MIT", "repository": "https://github.com/import-js/eslint-plugin-import", "publisher": "<PERSON>", "url": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-import-resolver-node", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-import-resolver-node/LICENSE"}, "eslint-module-utils@2.8.1": {"licenses": "MIT", "repository": "https://github.com/import-js/eslint-plugin-import", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-module-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-module-utils/LICENSE"}, "eslint-plugin-import@2.29.1": {"licenses": "MIT", "repository": "https://github.com/import-js/eslint-plugin-import", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import/LICENSE"}, "eslint-plugin-jest@27.9.0": {"licenses": "MIT", "repository": "https://github.com/jest-community/eslint-plugin-jest", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "jkimbo.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/LICENSE"}, "eslint-plugin-prettier@5.1.3": {"licenses": "MIT", "repository": "https://github.com/prettier/eslint-plugin-prettier", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-prettier", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-prettier/LICENSE.md"}, "eslint-scope@5.1.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/eslint/eslint-scope", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/eslint-scope", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/eslint-scope/LICENSE"}, "eslint-scope@7.2.2": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/eslint/eslint-scope", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-scope", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-scope/LICENSE"}, "eslint-visitor-keys@3.4.3": {"licenses": "Apache-2.0", "repository": "https://github.com/eslint/eslint-visitor-keys", "publisher": "<PERSON><PERSON>", "url": "https://github.com/mysticatea", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-visitor-keys", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-visitor-keys/LICENSE"}, "eslint@8.57.0": {"licenses": "MIT", "repository": "https://github.com/eslint/eslint", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint/LICENSE"}, "espree@9.6.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/eslint/espree", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/espree", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/espree/LICENSE"}, "esprima@4.0.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/jquery/esprima", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esprima", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esprima/LICENSE.BSD"}, "esquery@1.5.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/estools/esquery", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esquery", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esquery/license.txt"}, "esrecurse@4.3.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/esrecurse", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esrecurse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esrecurse/README.md"}, "estraverse@4.3.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/estraverse", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/estraverse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-jest/node_modules/estraverse/LICENSE.BSD"}, "estraverse@5.3.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/estraverse", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/estraverse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/estraverse/LICENSE.BSD"}, "esutils@2.0.3": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/estools/esutils", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esutils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/esutils/LICENSE.BSD"}, "etag@1.8.1": {"licenses": "MIT", "repository": "https://github.com/jshttp/etag", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/etag", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/etag/LICENSE"}, "eventemitter2@6.4.9": {"licenses": "MIT", "repository": "https://github.com/hij1nx/EventEmitter2", "publisher": "hij1nx", "email": "pao<PERSON>@async.ly", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eventemitter2", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eventemitter2/LICENSE.txt"}, "eventemitter3@3.1.2": {"licenses": "MIT", "repository": "https://github.com/primus/eventemitter3", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eventemitter3", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eventemitter3/LICENSE"}, "events@3.3.0": {"licenses": "MIT", "repository": "https://github.com/Gozala/events", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeditoolkit.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/events", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/events/LICENSE"}, "execa@5.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/execa", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/execa", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/execa/license"}, "execa@7.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/execa", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/execa", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/execa/license"}, "exit@0.1.2": {"licenses": "MIT", "repository": "https://github.com/cowboy/node-exit", "publisher": "\"Cowboy\" <PERSON>", "url": "http://benalman.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/exit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/exit/LICENSE-MIT"}, "expect@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/expect", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/expect/LICENSE"}, "express@4.19.2": {"licenses": "MIT", "repository": "https://github.com/expressjs/express", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/express", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/express/LICENSE"}, "express@4.21.2": {"licenses": "MIT", "repository": "https://github.com/expressjs/express", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/express", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/express/LICENSE"}, "external-editor@3.1.0": {"licenses": "MIT", "repository": "https://github.com/mrkmg/node-external-editor", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://mrkmg.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/external-editor", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/external-editor/LICENSE"}, "fast-deep-equal@3.1.3": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/fast-deep-equal", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-deep-equal", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-deep-equal/LICENSE"}, "fast-diff@1.3.0": {"licenses": "Apache-2.0", "repository": "https://github.com/jhchen/fast-diff", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-diff", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-diff/LICENSE"}, "fast-glob@3.3.2": {"licenses": "MIT", "repository": "https://github.com/mrmlnc/fast-glob", "publisher": "<PERSON>", "url": "https://mrmlnc.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-glob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-glob/LICENSE"}, "fast-json-stable-stringify@2.1.0": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/fast-json-stable-stringify", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-json-stable-stringify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-json-stable-stringify/LICENSE"}, "fast-levenshtein@2.0.6": {"licenses": "MIT", "repository": "https://github.com/hiddentao/fast-levenshtein", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.hiddentao.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-levenshtein", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-levenshtein/LICENSE.md"}, "fast-safe-stringify@2.1.1": {"licenses": "MIT", "repository": "https://github.com/davidmarkclements/fast-safe-stringify", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-safe-stringify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fast-safe-stringify/LICENSE"}, "fastq@1.17.1": {"licenses": "ISC", "repository": "https://github.com/mcollina/fastq", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fastq", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fastq/LICENSE"}, "fb-watchman@2.0.2": {"licenses": "Apache-2.0", "repository": "https://github.com/facebook/watchman", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fb-watchman", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fb-watchman/README.md"}, "fecha@4.2.3": {"licenses": "MIT", "repository": "git+https://<EMAIL>/taylorhakes/fecha", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fecha", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fecha/LICENSE"}, "fetch-blob@3.2.0": {"licenses": "MIT", "repository": "https://github.com/node-fetch/fetch-blob", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://jimmy.warting.se", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fetch-blob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fetch-blob/LICENSE"}, "figures@3.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/figures", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/figures", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/figures/license"}, "figures@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/figures", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/figures", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/figures/license"}, "file-entry-cache@6.0.1": {"licenses": "MIT", "repository": "https://github.com/royriojas/file-entry-cache", "publisher": "<PERSON>", "url": "http://royriojas.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/file-entry-cache", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/file-entry-cache/LICENSE"}, "fill-range@7.0.1": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/fill-range", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fill-range", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fill-range/LICENSE"}, "finalhandler@1.2.0": {"licenses": "MIT", "repository": "https://github.com/pillarjs/finalhandler", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/finalhandler", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/finalhandler/LICENSE"}, "finalhandler@1.3.1": {"licenses": "MIT", "repository": "https://github.com/pillarjs/finalhandler", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/finalhandler", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/finalhandler/LICENSE"}, "find-up@4.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/find-up", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/find-up", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/find-up/license"}, "find-up@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/find-up", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/find-up", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/find-up/license"}, "flat-cache@3.2.0": {"licenses": "MIT", "repository": "https://github.com/jaredwray/flat-cache", "publisher": "<PERSON>", "url": "https://jaredwray.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/flat-cache", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/flat-cache/LICENSE"}, "flatted@3.3.1": {"licenses": "ISC", "repository": "https://github.com/WebReflection/flatted", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/flatted", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/flatted/LICENSE"}, "fn.name@1.1.0": {"licenses": "MIT", "repository": "https://github.com/3rd-Eden/fn.name", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fn.name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fn.name/LICENSE"}, "follow-redirects@1.15.6": {"licenses": "MIT", "repository": "https://github.com/follow-redirects/follow-redirects", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://ruben.verborgh.org/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/follow-redirects", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/follow-redirects/LICENSE"}, "for-each@0.3.3": {"licenses": "MIT", "repository": "https://github.com/Raynos/for-each", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/for-each", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/for-each/LICENSE"}, "foreground-child@3.1.1": {"licenses": "ISC", "repository": "https://github.com/tapjs/foreground-child", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/foreground-child", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/foreground-child/LICENSE"}, "fork-ts-checker-webpack-plugin@9.0.2": {"licenses": "MIT", "repository": "https://github.com/TypeStrong/fork-ts-checker-webpack-plugin", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fork-ts-checker-webpack-plugin", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fork-ts-checker-webpack-plugin/LICENSE"}, "form-data-encoder@2.1.4": {"licenses": "MIT", "repository": "https://github.com/octet-stream/form-data-encoder", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/form-data-encoder", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/form-data-encoder/license"}, "form-data@4.0.0": {"licenses": "MIT", "repository": "https://github.com/form-data/form-data", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/form-data", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/form-data/License"}, "formdata-polyfill@4.0.10": {"licenses": "MIT", "repository": "git+https://<EMAIL>/jimmywarting/FormData", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/formdata-polyfill", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/formdata-polyfill/LICENSE"}, "formidable@2.1.2": {"licenses": "MIT", "repository": "https://github.com/node-formidable/formidable", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/formidable", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/formidable/LICENSE"}, "forwarded@0.2.0": {"licenses": "MIT", "repository": "https://github.com/jshttp/forwarded", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/forwarded", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/forwarded/LICENSE"}, "fresh@0.5.2": {"licenses": "MIT", "repository": "https://github.com/jshttp/fresh", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fresh", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fresh/LICENSE"}, "fs-extra@10.1.0": {"licenses": "MIT", "repository": "https://github.com/jprichardson/node-fs-extra", "publisher": "<PERSON>", "email": "jp<PERSON><PERSON><EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fs-extra", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fs-extra/LICENSE"}, "fs-monkey@1.0.6": {"licenses": "Unlicense", "repository": "https://github.com/streamich/fs-monkey", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fs-monkey", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fs-monkey/LICENSE"}, "fs.realpath@1.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/fs.realpath", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fs.realpath", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/fs.realpath/LICENSE"}, "function-bind@1.1.2": {"licenses": "MIT", "repository": "https://github.com/Raynos/function-bind", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/function-bind", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/function-bind/LICENSE"}, "function.prototype.name@1.1.8": {"licenses": "MIT", "repository": "https://github.com/es-shims/Function.prototype.name", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/function.prototype.name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/function.prototype.name/LICENSE"}, "functions-have-names@1.2.3": {"licenses": "MIT", "repository": "https://github.com/inspect-js/functions-have-names", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/functions-have-names", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/functions-have-names/LICENSE"}, "gensync@1.0.0-beta.2": {"licenses": "MIT", "repository": "https://github.com/loganfsmyth/gensync", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/gensync", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/gensync/LICENSE"}, "get-caller-file@2.0.5": {"licenses": "ISC", "repository": "https://github.com/stefanpenner/get-caller-file", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-caller-file", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-caller-file/LICENSE.md"}, "get-intrinsic@1.2.7": {"licenses": "MIT", "repository": "https://github.com/ljharb/get-intrinsic", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-intrinsic", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-intrinsic/LICENSE"}, "get-package-type@0.1.0": {"licenses": "MIT", "repository": "https://github.com/cfware/get-package-type", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-package-type", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-package-type/LICENSE"}, "get-proto@1.0.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/get-proto", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-proto", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-proto/LICENSE"}, "get-stream@6.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/get-stream", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-stream/license"}, "get-symbol-description@1.1.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/get-symbol-description", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-symbol-description", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-symbol-description/LICENSE"}, "get-uri@6.0.4": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-uri", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/get-uri/LICENSE"}, "git-up@7.0.0": {"licenses": "MIT", "repository": "https://github.com/IonicaBizau/git-up", "publisher": "Ionică Bizău", "email": "<EMAIL>", "url": "https://ionicabizau.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/git-up", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/git-up/LICENSE"}, "git-url-parse@13.1.0": {"licenses": "MIT", "repository": "https://github.com/IonicaBizau/git-url-parse", "publisher": "Ionică Bizău", "email": "<EMAIL>", "url": "https://ionicabizau.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/git-url-parse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/git-url-parse/LICENSE"}, "glob-parent@5.1.2": {"licenses": "ISC", "repository": "https://github.com/gulpjs/glob-parent", "publisher": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/glob-parent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/glob-parent/LICENSE"}, "glob-parent@6.0.2": {"licenses": "ISC", "repository": "https://github.com/gulpjs/glob-parent", "publisher": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint/node_modules/glob-parent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint/node_modules/glob-parent/LICENSE"}, "glob-to-regexp@0.4.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/fitzgen/glob-to-regexp", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/glob-to-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/glob-to-regexp/README.md"}, "glob@10.3.10": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-glob", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/glob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/glob/LICENSE"}, "glob@7.2.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-glob", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs/node_modules/glob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs/node_modules/glob/LICENSE"}, "glob@9.3.5": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-glob", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/rimraf/node_modules/glob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/rimraf/node_modules/glob/LICENSE"}, "global-dirs@3.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/global-dirs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/global-dirs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/global-dirs/license"}, "globals@11.12.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/globals", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/traverse/node_modules/globals", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/traverse/node_modules/globals/license"}, "globals@13.24.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/globals", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/globals", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/globals/license"}, "globalthis@1.0.4": {"licenses": "MIT", "repository": "https://github.com/ljharb/System.global", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/globalthis", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/globalthis/LICENSE"}, "globby@11.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/globby", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/globby", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/globby/license"}, "globby@13.2.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/globby", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/globby", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/globby/license"}, "gopd@1.2.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/gopd", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/gopd", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/gopd/LICENSE"}, "got@12.6.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/got", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/package-json/node_modules/got", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/package-json/node_modules/got/license"}, "got@13.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/got", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/got", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/got/license"}, "graceful-fs@4.2.10": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-graceful-fs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pnpm/network.ca-file/node_modules/graceful-fs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@pnpm/network.ca-file/node_modules/graceful-fs/LICENSE"}, "graceful-fs@4.2.11": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-graceful-fs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graceful-fs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graceful-fs/LICENSE"}, "graphemer@1.4.0": {"licenses": "MIT", "repository": "https://github.com/flmnt/graphemer", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphemer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphemer/LICENSE"}, "graphql-tag@2.12.6": {"licenses": "MIT", "repository": "https://github.com/apollographql/graphql-tag", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphql-tag", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphql-tag/LICENSE"}, "graphql-ws@5.14.2": {"licenses": "MIT", "repository": "https://github.com/enisdenjo/graphql-ws", "publisher": "<PERSON>", "email": "badurina<PERSON><PERSON>@gmail.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphql-ws", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphql-ws/LICENSE.md"}, "graphql@16.10.0": {"licenses": "MIT", "repository": "https://github.com/graphql/graphql-js", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphql", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/graphql/LICENSE"}, "has-bigints@1.1.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/has-bigints", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-bigints", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-bigints/LICENSE"}, "has-flag@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/has-flag", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/has-flag", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/has-flag/license"}, "has-flag@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/has-flag", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-flag", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-flag/license"}, "has-own-prop@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/has-own-prop", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-own-prop", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-own-prop/license"}, "has-property-descriptors@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/has-property-descriptors", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-property-descriptors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-property-descriptors/LICENSE"}, "has-proto@1.2.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/has-proto", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-proto", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-proto/LICENSE"}, "has-symbols@1.1.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/has-symbols", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-symbols", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-symbols/LICENSE"}, "has-tostringtag@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/has-tostringtag", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-tostringtag", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-tostringtag/LICENSE"}, "has-yarn@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/has-yarn", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-yarn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/has-yarn/license"}, "hasown@2.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/hasOwn", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/hasown", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/hasown/LICENSE"}, "hexoid@1.0.0": {"licenses": "MIT", "repository": "https://github.com/lukeed/hexoid", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/hexoid", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/hexoid/license"}, "html-escaper@2.0.2": {"licenses": "MIT", "repository": "https://github.com/WebReflection/html-escaper", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/html-escaper", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/html-escaper/LICENSE.txt"}, "http-cache-semantics@4.1.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/kornelski/http-cache-semantics", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://kornel.ski/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http-cache-semantics", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http-cache-semantics/LICENSE"}, "http-errors@2.0.0": {"licenses": "MIT", "repository": "https://github.com/jshttp/http-errors", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http-errors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http-errors/LICENSE"}, "http-proxy-agent@7.0.2": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http-proxy-agent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http-proxy-agent/LICENSE"}, "http2-wrapper@2.2.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/http2-wrapper", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http2-wrapper", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/http2-wrapper/LICENSE"}, "https-proxy-agent@7.0.6": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/https-proxy-agent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/https-proxy-agent/LICENSE"}, "human-signals@2.1.0": {"licenses": "Apache-2.0", "repository": "https://github.com/ehmicky/human-signals", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ehmicky", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/human-signals", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/human-signals/LICENSE"}, "human-signals@4.3.1": {"licenses": "Apache-2.0", "repository": "https://github.com/ehmicky/human-signals", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ehmicky", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/human-signals", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/human-signals/LICENSE"}, "husky@8.0.3": {"licenses": "MIT", "repository": "https://github.com/typicode/husky", "publisher": "Typicode", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/husky", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/husky/LICENSE"}, "iconv-lite@0.4.24": {"licenses": "MIT", "repository": "https://github.com/ashtuchkin/iconv-lite", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iconv-lite", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iconv-lite/LICENSE"}, "ieee754@1.2.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/feross/ieee754", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ieee754", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ieee754/LICENSE"}, "ignore@5.3.1": {"licenses": "MIT", "repository": "https://github.com/kaelzhang/node-ignore", "publisher": "kael", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ignore", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ignore/LICENSE-MIT"}, "immediate@3.0.6": {"licenses": "MIT", "repository": "https://github.com/calvinmetcalf/immediate", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/immediate", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/immediate/LICENSE.txt"}, "import-fresh@3.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/import-fresh", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/import-fresh", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/import-fresh/license"}, "import-lazy@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/import-lazy", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/import-lazy", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/import-lazy/license"}, "import-local@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/import-local", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/import-local", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/import-local/license"}, "imurmurhash@0.1.4": {"licenses": "MIT", "repository": "https://github.com/jensyt/imurmurhash-js", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/imurmurhash", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/imurmurhash/README.md"}, "inflight@1.0.6": {"licenses": "ISC", "repository": "https://github.com/npm/inflight", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inflight", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inflight/LICENSE"}, "inherits@2.0.4": {"licenses": "ISC", "repository": "https://github.com/isaacs/inherits", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inherits", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inherits/LICENSE"}, "ini@1.3.8": {"licenses": "ISC", "repository": "https://github.com/isaacs/ini", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/config-chain/node_modules/ini", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/config-chain/node_modules/ini/LICENSE"}, "ini@2.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/ini", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ini", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ini/LICENSE"}, "inquirer@8.2.6": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/Inquirer.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inquirer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inquirer/LICENSE"}, "inquirer@9.2.11": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/Inquirer.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/inquirer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/inquirer/LICENSE"}, "inquirer@9.2.12": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/Inquirer.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/schematics-cli/node_modules/inquirer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@angular-devkit/schematics-cli/node_modules/inquirer/LICENSE"}, "internal-slot@1.1.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/internal-slot", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/internal-slot", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/internal-slot/LICENSE"}, "interpret@1.4.0": {"licenses": "MIT", "repository": "https://github.com/gulpjs/interpret", "publisher": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/interpret", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/interpret/LICENSE"}, "ip-address@9.0.5": {"licenses": "MIT", "repository": "https://github.com/beaugunderson/ip-address", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://beaugunderson.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ip-address", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ip-address/LICENSE"}, "ipaddr.js@1.9.1": {"licenses": "MIT", "repository": "https://github.com/whitequark/ipaddr.js", "publisher": "whitequark", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ipaddr.js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ipaddr.js/LICENSE"}, "is-arguments@1.2.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-arguments", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-arguments", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-arguments/LICENSE"}, "is-array-buffer@3.0.5": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-array-buffer", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-array-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-array-buffer/LICENSE"}, "is-arrayish@0.2.1": {"licenses": "MIT", "repository": "https://github.com/qix-/node-is-arrayish", "publisher": "Qi<PERSON>", "url": "http://github.com/qix-", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-arrayish", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-arrayish/LICENSE"}, "is-arrayish@0.3.2": {"licenses": "MIT", "repository": "https://github.com/qix-/node-is-arrayish", "publisher": "Qi<PERSON>", "url": "http://github.com/qix-", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/simple-swizzle/node_modules/is-arrayish", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/simple-swizzle/node_modules/is-arrayish/LICENSE"}, "is-async-function@2.1.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-async-function", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-async-function", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-async-function/LICENSE"}, "is-bigint@1.1.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-bigint", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-bigint", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-bigint/LICENSE"}, "is-binary-path@2.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-binary-path", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-binary-path", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-binary-path/license"}, "is-boolean-object@1.2.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-boolean-object", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-boolean-object", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-boolean-object/LICENSE"}, "is-callable@1.2.7": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-callable", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-callable", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-callable/LICENSE"}, "is-ci@3.0.1": {"licenses": "MIT", "repository": "https://github.com/watson/is-ci", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://twitter.com/wa7son", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-ci", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-ci/LICENSE"}, "is-core-module@2.13.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-core-module", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-core-module", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-core-module/LICENSE"}, "is-data-view@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-data-view", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-data-view", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-data-view/LICENSE"}, "is-date-object@1.1.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-date-object", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-date-object", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-date-object/LICENSE"}, "is-docker@2.2.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-docker", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-wsl/node_modules/is-docker", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-wsl/node_modules/is-docker/license"}, "is-docker@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-docker", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-docker", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-docker/license"}, "is-extglob@2.1.1": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/is-extglob", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-extglob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-extglob/LICENSE"}, "is-finalizationregistry@1.1.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-finalizationregistry", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-finalizationregistry", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-finalizationregistry/LICENSE"}, "is-fullwidth-code-point@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-fullwidth-code-point", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-fullwidth-code-point", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-fullwidth-code-point/license"}, "is-generator-fn@2.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-generator-fn", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-generator-fn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-generator-fn/license"}, "is-generator-function@1.1.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-generator-function", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-generator-function", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-generator-function/LICENSE"}, "is-glob@4.0.3": {"licenses": "MIT", "repository": "https://github.com/micromatch/is-glob", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-glob", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-glob/LICENSE"}, "is-inside-container@1.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-inside-container", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-inside-container", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-inside-container/license"}, "is-installed-globally@0.4.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-installed-globally", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-installed-globally", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-installed-globally/license"}, "is-interactive@1.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-interactive", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-interactive", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-interactive/license"}, "is-interactive@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-interactive", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/is-interactive", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/is-interactive/license"}, "is-map@2.0.3": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-map", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-map/LICENSE"}, "is-npm@6.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-npm", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-npm", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-npm/license"}, "is-number-object@1.1.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-number-object", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-number-object", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-number-object/LICENSE"}, "is-number@7.0.0": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/is-number", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-number", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-number/LICENSE"}, "is-obj@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-obj", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-obj", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-obj/license"}, "is-path-inside@3.0.3": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-path-inside", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-path-inside", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-path-inside/license"}, "is-plain-object@5.0.0": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/is-plain-object", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-plain-object", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-plain-object/LICENSE"}, "is-regex@1.2.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-regex", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-regex/LICENSE"}, "is-set@2.0.3": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-set", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-set", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-set/LICENSE"}, "is-shared-array-buffer@1.0.4": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-shared-array-buffer", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-shared-array-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-shared-array-buffer/LICENSE"}, "is-ssh@1.4.0": {"licenses": "MIT", "repository": "https://github.com/IonicaBizau/node-is-ssh", "publisher": "Ionică Bizău", "email": "<EMAIL>", "url": "http://ionicabizau.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-ssh", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-ssh/LICENSE"}, "is-stream@2.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-stream", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-stream/license"}, "is-stream@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-stream", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/is-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/is-stream/license"}, "is-string@1.1.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-string", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-string/LICENSE"}, "is-symbol@1.1.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-symbol", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-symbol", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-symbol/LICENSE"}, "is-typed-array@1.1.15": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-typed-array", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-typed-array", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-typed-array/LICENSE"}, "is-typedarray@1.0.0": {"licenses": "MIT", "repository": "https://github.com/hughsk/is-typedarray", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://hughsk.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-typedarray", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-typedarray/LICENSE.md"}, "is-unicode-supported@0.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-unicode-supported", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-unicode-supported", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-unicode-supported/license"}, "is-unicode-supported@1.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-unicode-supported", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/figures/node_modules/is-unicode-supported", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/figures/node_modules/is-unicode-supported/license"}, "is-weakmap@2.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-weakmap", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-weakmap", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-weakmap/LICENSE"}, "is-weakref@1.1.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-weakref", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-weakref", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-weakref/LICENSE"}, "is-weakset@2.0.4": {"licenses": "MIT", "repository": "https://github.com/inspect-js/is-weakset", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-weakset", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-weakset/LICENSE"}, "is-wsl@2.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/is-wsl", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-wsl", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-wsl/license"}, "is-yarn-global@0.4.1": {"licenses": "MIT", "repository": "https://github.com/LitoMore/is-yarn-global", "publisher": "LitoMore", "url": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-yarn-global", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/is-yarn-global/LICENSE"}, "isarray@0.0.1": {"licenses": "MIT", "repository": "https://github.com/juliangruber/isarray", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/isarray", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/isarray/README.md"}, "isarray@1.0.0": {"licenses": "MIT", "repository": "https://github.com/juliangruber/isarray", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream/node_modules/isarray", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream/node_modules/isarray/README.md"}, "isarray@2.0.5": {"licenses": "MIT", "repository": "https://github.com/juliangruber/isarray", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-push-apply/node_modules/isarray", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-push-apply/node_modules/isarray/LICENSE"}, "isexe@2.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/isexe", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/isexe", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/isexe/LICENSE"}, "issue-parser@6.0.0": {"licenses": "MIT", "repository": "https://github.com/pvdlg/issue-parser", "publisher": "<PERSON>", "url": "https://github.com/pvdlg", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/issue-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/issue-parser/LICENSE"}, "istanbul-lib-coverage@3.2.2": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/istanbuljs/istanbuljs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-coverage", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-coverage/LICENSE"}, "istanbul-lib-instrument@5.2.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/istanbuljs/istanbuljs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument/LICENSE"}, "istanbul-lib-instrument@6.0.2": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/istanbuljs/istanbuljs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-instrument", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-instrument/LICENSE"}, "istanbul-lib-report@3.0.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/istanbuljs/istanbuljs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-report", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-report/LICENSE"}, "istanbul-lib-source-maps@4.0.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/istanbuljs/istanbuljs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-source-maps", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-lib-source-maps/LICENSE"}, "istanbul-reports@3.1.7": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/istanbuljs/istanbuljs", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-reports", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/istanbul-reports/LICENSE"}, "iterall@1.3.0": {"licenses": "MIT", "repository": "https://github.com/leebyron/iterall", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://leebyron.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterall", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterall/LICENSE"}, "iterare@1.2.1": {"licenses": "ISC", "repository": "https://github.com/felixfbecker/iterare", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterare", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterare/LICENSE.txt"}, "iterate-iterator@1.0.2": {"licenses": "MIT", "repository": "https://github.com/ljharb/iterate-iterator", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterate-iterator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterate-iterator/LICENSE"}, "iterate-value@1.0.2": {"licenses": "MIT", "repository": "https://github.com/ljharb/iterate-value", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterate-value", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/iterate-value/LICENSE"}, "jackspeak@2.3.6": {"licenses": "BlueOak-1.0.0", "repository": "https://github.com/isaacs/jackspeak", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jackspeak", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jackspeak/LICENSE.md"}, "jest-changed-files@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-changed-files", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-changed-files/LICENSE"}, "jest-circus@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-circus", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-circus/LICENSE"}, "jest-cli@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-cli", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-cli/LICENSE"}, "jest-config@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-config", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-config/LICENSE"}, "jest-diff@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-diff", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-diff/LICENSE"}, "jest-docblock@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-docblock", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-docblock/LICENSE"}, "jest-each@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "publisher": "<PERSON>", "url": "mat<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-each", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-each/LICENSE"}, "jest-environment-node@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-environment-node", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-environment-node/LICENSE"}, "jest-get-type@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-get-type", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-get-type/LICENSE"}, "jest-haste-map@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-haste-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-haste-map/LICENSE"}, "jest-leak-detector@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-leak-detector", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-leak-detector/LICENSE"}, "jest-matcher-utils@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-matcher-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-matcher-utils/LICENSE"}, "jest-message-util@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-message-util", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-message-util/LICENSE"}, "jest-mock@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-mock", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-mock/LICENSE"}, "jest-pnp-resolver@1.2.3": {"licenses": "MIT", "repository": "https://github.com/arcanis/jest-pnp-resolver", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-pnp-resolver", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-pnp-resolver/README.md"}, "jest-regex-util@29.6.3": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-regex-util", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-regex-util/LICENSE"}, "jest-resolve-dependencies@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-resolve-dependencies", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-resolve-dependencies/LICENSE"}, "jest-resolve@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-resolve", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-resolve/LICENSE"}, "jest-runner@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-runner", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-runner/LICENSE"}, "jest-runtime@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-runtime", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-runtime/LICENSE"}, "jest-snapshot@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-snapshot", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-snapshot/LICENSE"}, "jest-util@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-util", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-util/LICENSE"}, "jest-validate@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-validate", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-validate/LICENSE"}, "jest-watcher@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-watcher", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-watcher/LICENSE"}, "jest-worker@27.5.1": {"licenses": "MIT", "repository": "https://github.com/facebook/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser-webpack-plugin/node_modules/jest-worker", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser-webpack-plugin/node_modules/jest-worker/LICENSE"}, "jest-worker@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-worker", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-worker/LICENSE"}, "jest@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest/LICENSE"}, "js-tokens@4.0.0": {"licenses": "MIT", "repository": "https://github.com/lydell/js-tokens", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/js-tokens", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/js-tokens/LICENSE"}, "js-yaml@3.14.1": {"licenses": "MIT", "repository": "https://github.com/nodeca/js-yaml", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml/LICENSE"}, "js-yaml@4.1.0": {"licenses": "MIT", "repository": "https://github.com/nodeca/js-yaml", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/js-yaml", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/js-yaml/LICENSE"}, "jsbn@1.1.0": {"licenses": "MIT", "repository": "https://github.com/andyperlitch/jsbn", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsbn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsbn/LICENSE"}, "jsesc@2.5.2": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/jsesc", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsesc", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsesc/LICENSE-MIT.txt"}, "json-buffer@3.0.1": {"licenses": "MIT", "repository": "https://github.com/dominictarr/json-buffer", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-buffer/LICENSE"}, "json-parse-even-better-errors@2.3.1": {"licenses": "MIT", "repository": "https://github.com/npm/json-parse-even-better-errors", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-parse-even-better-errors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-parse-even-better-errors/LICENSE.md"}, "json-schema-traverse@0.4.1": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/json-schema-traverse", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils/node_modules/json-schema-traverse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils/node_modules/json-schema-traverse/LICENSE"}, "json-schema-traverse@1.0.0": {"licenses": "MIT", "repository": "https://github.com/epoberezkin/json-schema-traverse", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-schema-traverse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-schema-traverse/LICENSE"}, "json-stable-stringify-without-jsonify@1.0.1": {"licenses": "MIT", "repository": "https://github.com/samn/json-stable-stringify", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-stable-stringify-without-jsonify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json-stable-stringify-without-jsonify/LICENSE"}, "json5@1.0.2": {"licenses": "MIT", "repository": "https://github.com/json5/json5", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import/node_modules/json5", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import/node_modules/json5/LICENSE.md"}, "json5@2.2.3": {"licenses": "MIT", "repository": "https://github.com/json5/json5", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json5", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/json5/LICENSE.md"}, "jsonc-parser@3.2.0": {"licenses": "MIT", "repository": "https://github.com/microsoft/node-jsonc-parser", "publisher": "Microsoft Corporation", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsonc-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsonc-parser/LICENSE.md"}, "jsonc-parser@3.2.1": {"licenses": "MIT", "repository": "https://github.com/microsoft/node-jsonc-parser", "publisher": "Microsoft Corporation", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/schematics/node_modules/jsonc-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/schematics/node_modules/jsonc-parser/LICENSE.md"}, "jsonfile@6.1.0": {"licenses": "MIT", "repository": "https://github.com/jprichardson/node-jsonfile", "publisher": "<PERSON>", "email": "jp<PERSON><PERSON><EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsonfile", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jsonfile/LICENSE"}, "keyv@4.5.4": {"licenses": "MIT", "repository": "https://github.com/jaredwray/keyv", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/keyv", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/keyv/README.md"}, "kleur@3.0.3": {"licenses": "MIT", "repository": "https://github.com/lukeed/kleur", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/kleur", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/kleur/license"}, "kuler@2.0.0": {"licenses": "MIT", "repository": "https://github.com/3rd-Eden/kuler", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/kuler", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/kuler/LICENSE"}, "latest-version@7.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/latest-version", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/latest-version", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/latest-version/license"}, "leven@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/leven", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/leven", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/leven/license"}, "levn@0.4.1": {"licenses": "MIT", "repository": "https://github.com/gkz/levn", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/levn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/levn/LICENSE"}, "libphonenumber-js@1.11.1": {"licenses": "MIT", "repository": "git+https://gitlab.com/catamphetamine/libphonenumber-js", "publisher": "catamphetamine", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/libphonenumber-js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/libphonenumber-js/LICENSE"}, "lie@3.1.1": {"licenses": "MIT", "repository": "https://github.com/calvinmetcalf/lie", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lie", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lie/license.md"}, "lines-and-columns@1.2.4": {"licenses": "MIT", "repository": "https://github.com/eventualbuddha/lines-and-columns", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lines-and-columns", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lines-and-columns/LICENSE"}, "loader-runner@4.3.0": {"licenses": "MIT", "repository": "https://github.com/webpack/loader-runner", "publisher": "<PERSON> @sokra", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/loader-runner", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/loader-runner/LICENSE"}, "localforage@1.10.0": {"licenses": "Apache-2.0", "repository": "https://github.com/localForage/localForage", "publisher": "Mozilla", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/localforage", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/localforage/LICENSE"}, "locate-path@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/locate-path", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/locate-path", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/locate-path/license"}, "locate-path@6.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/locate-path", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/locate-path", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/locate-path/license"}, "lodash.capitalize@4.2.1": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.capitalize", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.capitalize/LICENSE"}, "lodash.escaperegexp@4.1.2": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.escaperegexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.escaperegexp/LICENSE"}, "lodash.isequal@4.5.0": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.isequal", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.isequal/LICENSE"}, "lodash.isplainobject@4.0.6": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.isplainobject", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.isplainobject/LICENSE"}, "lodash.isstring@4.0.1": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.isstring", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.isstring/LICENSE"}, "lodash.memoize@4.1.2": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.memoize", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.memoize/LICENSE"}, "lodash.merge@4.6.2": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.merge", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.merge/LICENSE"}, "lodash.uniqby@4.7.0": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://allyoucanleet.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.uniqby", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash.uniqby/LICENSE"}, "lodash@4.17.21": {"licenses": "MIT", "repository": "https://github.com/lodash/lodash", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lodash/LICENSE"}, "log-symbols@4.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/log-symbols", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/log-symbols", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/log-symbols/license"}, "log-symbols@5.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/log-symbols", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/log-symbols", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/log-symbols/license"}, "logform@2.6.0": {"licenses": "MIT", "repository": "https://github.com/winstonjs/logform", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/logform", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/logform/LICENSE"}, "lowercase-keys@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/lowercase-keys", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lowercase-keys", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lowercase-keys/license"}, "lru-cache@10.2.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-lru-cache", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-scurry/node_modules/lru-cache", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-scurry/node_modules/lru-cache/LICENSE"}, "lru-cache@5.1.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-lru-cache", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lru-cache", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/lru-cache/LICENSE"}, "lru-cache@6.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-lru-cache", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/lru-cache", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/lru-cache/LICENSE"}, "lru-cache@7.18.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-lru-cache", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-agent/node_modules/lru-cache", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-agent/node_modules/lru-cache/LICENSE"}, "luxon@3.3.0": {"licenses": "MIT", "repository": "https://github.com/moment/luxon", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/luxon", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/luxon/LICENSE.md"}, "macos-release@3.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/macos-release", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/macos-release", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/macos-release/license"}, "magic-string@0.30.5": {"licenses": "MIT", "repository": "https://github.com/rich-harris/magic-string", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/magic-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/magic-string/LICENSE"}, "make-dir@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/make-dir", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/make-dir", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/make-dir/license"}, "make-error@1.3.6": {"licenses": "ISC", "repository": "https://github.com/JsCommunity/make-error", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/make-error", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/make-error/LICENSE"}, "makeerror@1.0.12": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/daaku/nodejs-makeerror", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/makeerror", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/makeerror/license"}, "math-intrinsics@1.1.0": {"licenses": "MIT", "repository": "https://github.com/es-shims/math-intrinsics", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/math-intrinsics", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/math-intrinsics/LICENSE"}, "media-typer@0.3.0": {"licenses": "MIT", "repository": "https://github.com/jshttp/media-typer", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/media-typer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/media-typer/LICENSE"}, "memfs@3.6.0": {"licenses": "Unlicense", "repository": "https://github.com/streamich/memfs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/memfs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/memfs/LICENSE"}, "merge-descriptors@1.0.1": {"licenses": "MIT", "repository": "https://github.com/component/merge-descriptors", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/merge-descriptors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/merge-descriptors/LICENSE"}, "merge-descriptors@1.0.3": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/merge-descriptors", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/merge-descriptors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/merge-descriptors/LICENSE"}, "merge-stream@2.0.0": {"licenses": "MIT", "repository": "https://github.com/grncdr/merge-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/merge-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/merge-stream/LICENSE"}, "merge2@1.4.1": {"licenses": "MIT", "repository": "https://github.com/teambition/merge2", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/merge2", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/merge2/LICENSE"}, "methods@1.1.2": {"licenses": "MIT", "repository": "https://github.com/jshttp/methods", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/methods", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/methods/LICENSE"}, "micromatch@4.0.5": {"licenses": "MIT", "repository": "https://github.com/micromatch/micromatch", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/micromatch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/micromatch/LICENSE"}, "mime-db@1.52.0": {"licenses": "MIT", "repository": "https://github.com/jshttp/mime-db", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mime-db", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mime-db/LICENSE"}, "mime-types@2.1.35": {"licenses": "MIT", "repository": "https://github.com/jshttp/mime-types", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mime-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mime-types/LICENSE"}, "mime@1.6.0": {"licenses": "MIT", "repository": "https://github.com/broofa/node-mime", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mime", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mime/LICENSE"}, "mime@2.6.0": {"licenses": "MIT", "repository": "https://github.com/broofa/mime", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/broofa", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/superagent/node_modules/mime", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/superagent/node_modules/mime/LICENSE"}, "mimic-fn@2.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/mimic-fn", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mimic-fn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mimic-fn/license"}, "mimic-fn@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/mimic-fn", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/mimic-fn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/mimic-fn/license"}, "mimic-response@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/mimic-response", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/decompress-response/node_modules/mimic-response", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/decompress-response/node_modules/mimic-response/license"}, "mimic-response@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/mimic-response", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mimic-response", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mimic-response/license"}, "minimatch@3.1.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/minimatch", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs/node_modules/minimatch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs/node_modules/minimatch/LICENSE"}, "minimatch@8.0.4": {"licenses": "ISC", "repository": "https://github.com/isaacs/minimatch", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/minimatch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/minimatch/LICENSE"}, "minimatch@9.0.3": {"licenses": "ISC", "repository": "https://github.com/isaacs/minimatch", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/minimatch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/minimatch/LICENSE"}, "minimist@1.2.8": {"licenses": "MIT", "repository": "https://github.com/minimistjs/minimist", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/minimist", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/minimist/LICENSE"}, "minipass@4.2.8": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/minipass", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/minipass/LICENSE"}, "minipass@7.1.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/minipass", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/minipass", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/minipass/LICENSE"}, "mkdirp@0.5.6": {"licenses": "MIT", "repository": "https://github.com/substack/node-mkdirp", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mkdirp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mkdirp/LICENSE"}, "ms@2.0.0": {"licenses": "MIT", "repository": "https://github.com/zeit/ms", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/body-parser/node_modules/ms", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/body-parser/node_modules/ms/license.md"}, "ms@2.1.2": {"licenses": "MIT", "repository": "https://github.com/zeit/ms", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ms", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ms/license.md"}, "ms@2.1.3": {"licenses": "MIT", "repository": "https://github.com/vercel/ms", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/send/node_modules/ms", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/send/node_modules/ms/license.md"}, "multer@1.4.4-lts.1": {"licenses": "MIT", "repository": "https://github.com/expressjs/multer", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/multer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/multer/LICENSE"}, "mute-stream@0.0.8": {"licenses": "ISC", "repository": "https://github.com/isaacs/mute-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mute-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/mute-stream/LICENSE"}, "mute-stream@1.0.0": {"licenses": "ISC", "repository": "https://github.com/npm/mute-stream", "publisher": "GitHub Inc.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/mute-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/mute-stream/LICENSE"}, "natural-compare@1.4.0": {"licenses": "MIT", "repository": "https://github.com/litejs/natural-compare-lite", "publisher": "<PERSON><PERSON>", "url": "https://github.com/litejs/natural-compare-lite", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/natural-compare", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/natural-compare/README.md"}, "negotiator@0.6.3": {"licenses": "MIT", "repository": "https://github.com/jshttp/negotiator", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/negotiator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/negotiator/LICENSE"}, "neo-async@2.6.2": {"licenses": "MIT", "repository": "https://github.com/suguru03/neo-async", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/neo-async", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/neo-async/LICENSE"}, "nest-commander@3.15.0": {"licenses": "MIT", "repository": "https://github.com/jmcdo29/nest-commander", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/nest-commander", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/nest-commander/LICENSE"}, "netmask@2.0.2": {"licenses": "MIT", "repository": "https://github.com/rs/node-netmask", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/netmask", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/netmask/README.md"}, "new-github-release-url@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/new-github-release-url", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/new-github-release-url", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/new-github-release-url/license"}, "node-abort-controller@3.1.1": {"licenses": "MIT", "repository": "https://github.com/southpolesteve/node-abort-controller", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-abort-controller", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-abort-controller/LICENSE"}, "node-domexception@1.0.0": {"licenses": "MIT", "repository": "https://github.com/jimmywarting/node-domexception", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-domexception", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-domexception/LICENSE"}, "node-emoji@1.11.0": {"licenses": "MIT", "repository": "https://github.com/omnidan/node-emoji", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-emoji", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-emoji/LICENSE"}, "node-fetch@2.7.0": {"licenses": "MIT", "repository": "https://github.com/bitinn/node-fetch", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-fetch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-fetch/LICENSE.md"}, "node-fetch@3.3.2": {"licenses": "MIT", "repository": "https://github.com/node-fetch/node-fetch", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/node-fetch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/node-fetch/LICENSE.md"}, "node-int64@0.4.0": {"licenses": "MIT", "repository": "https://github.com/broofa/node-int64", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-int64", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-int64/LICENSE"}, "node-releases@2.0.19": {"licenses": "MIT", "repository": "https://github.com/chicoxyzzy/node-releases", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-releases", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/node-releases/LICENSE"}, "normalize-path@3.0.0": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/normalize-path", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/normalize-path", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/normalize-path/LICENSE"}, "normalize-url@8.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/normalize-url", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/normalize-url", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/normalize-url/license"}, "npm-run-path@4.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/npm-run-path", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/npm-run-path", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/npm-run-path/license"}, "npm-run-path@5.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/npm-run-path", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/npm-run-path", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/npm-run-path/license"}, "object-assign@4.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/object-assign", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object-assign", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object-assign/license"}, "object-inspect@1.13.3": {"licenses": "MIT", "repository": "https://github.com/inspect-js/object-inspect", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object-inspect", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object-inspect/LICENSE"}, "object-keys@1.1.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/object-keys", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object-keys", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object-keys/LICENSE"}, "object.assign@4.1.7": {"licenses": "MIT", "repository": "https://github.com/ljharb/object.assign", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.assign", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.assign/LICENSE"}, "object.entries@1.1.8": {"licenses": "MIT", "repository": "https://github.com/es-shims/Object.entries", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.entries", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.entries/LICENSE"}, "object.fromentries@2.0.8": {"licenses": "MIT", "repository": "https://github.com/es-shims/Object.fromEntries", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.fromentries", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.fromentries/LICENSE"}, "object.groupby@1.0.3": {"licenses": "MIT", "repository": "https://github.com/es-shims/Object.groupBy", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.groupby", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.groupby/LICENSE"}, "object.values@1.2.0": {"licenses": "MIT", "repository": "https://github.com/es-shims/Object.values", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.values", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/object.values/LICENSE"}, "obuf@1.1.2": {"licenses": "MIT", "repository": "https://github.com/indutny/offset-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/obuf", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/obuf/LICENSE"}, "on-finished@2.4.1": {"licenses": "MIT", "repository": "https://github.com/jshttp/on-finished", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/on-finished", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/on-finished/LICENSE"}, "once@1.4.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/once", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/once", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/once/LICENSE"}, "one-time@1.0.0": {"licenses": "MIT", "repository": "https://github.com/3rd-Eden/one-time", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/one-time", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/one-time/LICENSE"}, "onetime@5.1.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/onetime", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/onetime", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/onetime/license"}, "onetime@6.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/onetime", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/onetime", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/onetime/license"}, "open@9.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/open", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/open", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/open/license"}, "optionator@0.9.4": {"licenses": "MIT", "repository": "https://github.com/gkz/optionator", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/optionator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/optionator/LICENSE"}, "ora@5.4.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/ora", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ora", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ora/license"}, "ora@7.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/ora", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/license"}, "os-name@5.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/os-name", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/os-name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/os-name/license"}, "os-tmpdir@1.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/os-tmpdir", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/os-tmpdir", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/os-tmpdir/license"}, "own-keys@1.0.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/own-keys", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/own-keys", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/own-keys/LICENSE"}, "p-cancelable@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-cancelable", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-cancelable", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-cancelable/license"}, "p-limit@2.3.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-limit", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/p-limit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/p-limit/license"}, "p-limit@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-limit", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-limit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-limit/license"}, "p-locate@4.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-locate", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/p-locate", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/p-locate/license"}, "p-locate@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-locate", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-locate", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-locate/license"}, "p-try@2.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/p-try", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-try", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/p-try/license"}, "pac-proxy-agent@7.1.0": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pac-proxy-agent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pac-proxy-agent/LICENSE"}, "pac-resolver@7.0.1": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pac-resolver", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pac-resolver/LICENSE"}, "package-json@8.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/package-json", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/package-json", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/package-json/license"}, "parent-module@1.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/parent-module", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parent-module", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parent-module/license"}, "parse-json@5.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/parse-json", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parse-json", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parse-json/license"}, "parse-path@7.0.0": {"licenses": "MIT", "repository": "https://github.com/IonicaBizau/parse-path", "publisher": "Ionică Bizău", "email": "<EMAIL>", "url": "https://ionicabizau.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parse-path", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parse-path/LICENSE"}, "parse-url@8.1.0": {"licenses": "MIT", "repository": "https://github.com/IonicaBizau/parse-url", "publisher": "Ionică Bizău", "email": "<EMAIL>", "url": "https://ionicabizau.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parse-url", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parse-url/LICENSE"}, "parseurl@1.3.3": {"licenses": "MIT", "repository": "https://github.com/pillarjs/parseurl", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parseurl", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/parseurl/LICENSE"}, "path-exists@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-exists", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-exists", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-exists/license"}, "path-is-absolute@1.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-is-absolute", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-is-absolute", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-is-absolute/license"}, "path-key@3.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-key", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-key", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-key/license"}, "path-key@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-key", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/path-key", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/path-key/license"}, "path-parse@1.0.7": {"licenses": "MIT", "repository": "https://github.com/jbgu<PERSON>rez/path-parse", "publisher": "<PERSON>", "email": "http://jbgutierrez.info", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-parse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-parse/LICENSE"}, "path-scurry@1.11.1": {"licenses": "BlueOak-1.0.0", "repository": "https://github.com/isaacs/path-scurry", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://blog.izs.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-scurry", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-scurry/LICENSE.md"}, "path-to-regexp@0.1.12": {"licenses": "MIT", "repository": "https://github.com/pillarjs/path-to-regexp", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/path-to-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/path-to-regexp/LICENSE"}, "path-to-regexp@0.1.7": {"licenses": "MIT", "repository": "https://github.com/component/path-to-regexp", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/express/node_modules/path-to-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/express/node_modules/path-to-regexp/LICENSE"}, "path-to-regexp@3.2.0": {"licenses": "MIT", "repository": "https://github.com/pillarjs/path-to-regexp", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-to-regexp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-to-regexp/LICENSE"}, "path-type@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/path-type", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-type", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/path-type/license"}, "pg-cloudflare@1.1.1": {"licenses": "MIT", "repository": "https://github.com/brianc/node-postgres", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-cloudflare", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-cloudflare/LICENSE"}, "pg-connection-string@2.7.0": {"licenses": "MIT", "repository": "https://github.com/brianc/node-postgres", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://iceddev.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-connection-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-connection-string/LICENSE"}, "pg-int8@1.0.1": {"licenses": "ISC", "repository": "https://github.com/charmander/pg-int8", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-int8", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-int8/LICENSE"}, "pg-numeric@1.0.2": {"licenses": "ISC", "repository": "https://github.com/charmander/pg-numeric", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-numeric", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-numeric/LICENSE"}, "pg-pool@3.7.0": {"licenses": "MIT", "repository": "https://github.com/brianc/node-postgres", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-pool", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-pool/LICENSE"}, "pg-protocol@1.7.0": {"licenses": "MIT", "repository": "https://github.com/brianc/node-postgres", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-protocol", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-protocol/LICENSE"}, "pg-types@2.2.0": {"licenses": "MIT", "repository": "https://github.com/brianc/node-pg-types", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/pg-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/pg-types/README.md"}, "pg-types@4.0.2": {"licenses": "MIT", "repository": "https://github.com/brianc/node-pg-types", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg-types/README.md"}, "pg@8.13.1": {"licenses": "MIT", "repository": "https://github.com/brianc/node-postgres", "publisher": "<PERSON>", "email": "brian.m.car<PERSON>@gmail.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/LICENSE"}, "pgpass@1.0.5": {"licenses": "MIT", "repository": "https://github.com/hoegaarden/pgpass", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pgpass", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pgpass/README.md"}, "picocolors@1.1.1": {"licenses": "ISC", "repository": "https://github.com/alexeyraspopov/picocolors", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/picocolors", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/picocolors/LICENSE"}, "picomatch@2.3.1": {"licenses": "MIT", "repository": "https://github.com/micromatch/picomatch", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/micromatch/node_modules/picomatch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/micromatch/node_modules/picomatch/LICENSE"}, "picomatch@3.0.1": {"licenses": "MIT", "repository": "https://github.com/micromatch/picomatch", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/picomatch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/picomatch/LICENSE"}, "pirates@4.0.6": {"licenses": "MIT", "repository": "https://github.com/danez/pirates", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ariporad.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pirates", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pirates/LICENSE"}, "pkg-dir@4.2.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/pkg-dir", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pkg-dir", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pkg-dir/license"}, "pluralize@8.0.0": {"licenses": "MIT", "repository": "https://github.com/blakeembrey/pluralize", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pluralize", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pluralize/LICENSE"}, "possible-typed-array-names@1.0.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/possible-typed-array-names", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/possible-typed-array-names", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/possible-typed-array-names/LICENSE"}, "postgres-array@2.0.0": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-array", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-array", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-array/license"}, "postgres-array@3.0.2": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-array", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-array", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-array/license"}, "postgres-bytea@1.0.0": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-bytea", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-bytea", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-bytea/license"}, "postgres-bytea@3.0.0": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-bytea", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-bytea", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-bytea/license"}, "postgres-date@1.0.7": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-date", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-date", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-date/license"}, "postgres-date@2.1.0": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-date", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-date", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-date/license"}, "postgres-interval@1.2.0": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-interval", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-interval", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pg/node_modules/postgres-interval/license"}, "postgres-interval@3.0.0": {"licenses": "MIT", "repository": "https://github.com/bendrucker/postgres-interval", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://www.bendrucker.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-interval", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-interval/license"}, "postgres-range@1.1.4": {"licenses": "MIT", "repository": "https://github.com/martianboy/postgres-range", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-range", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/postgres-range/LICENSE"}, "prelude-ls@1.2.1": {"licenses": "MIT", "repository": "https://github.com/gkz/prelude-ls", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prelude-ls", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prelude-ls/LICENSE"}, "prettier-linter-helpers@1.0.0": {"licenses": "MIT", "repository": "https://github.com/prettier/prettier-linter-helpers", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prettier-linter-helpers", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prettier-linter-helpers/LICENSE.md"}, "prettier@3.2.5": {"licenses": "MIT", "repository": "https://github.com/prettier/prettier", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prettier", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prettier/LICENSE"}, "pretty-format@29.7.0": {"licenses": "MIT", "repository": "https://github.com/jestjs/jest", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pretty-format", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pretty-format/LICENSE"}, "prisma@5.13.0": {"licenses": "Apache-2.0", "repository": "https://github.com/prisma/prisma", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prisma", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prisma/LICENSE"}, "process-nextick-args@2.0.1": {"licenses": "MIT", "repository": "https://github.com/calvinmetcalf/process-nextick-args", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/process-nextick-args", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/process-nextick-args/license.md"}, "promise-breaker@6.0.0": {"licenses": "MIT", "repository": "https://github.com/jwalton/node-promise-breaker", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/jwalton", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/promise-breaker", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/promise-breaker/LICENSE"}, "promise.allsettled@1.0.7": {"licenses": "MIT", "repository": "https://github.com/es-shims/Promise.allSettled", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/promise.allsettled", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/promise.allsettled/LICENSE"}, "prompts@2.4.2": {"licenses": "MIT", "repository": "https://github.com/terkelg/prompts", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prompts", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/prompts/license"}, "proto-list@1.2.4": {"licenses": "ISC", "repository": "https://github.com/isaacs/proto-list", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proto-list", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proto-list/LICENSE"}, "protocols@2.0.1": {"licenses": "MIT", "repository": "https://github.com/IonicaBizau/protocols", "publisher": "Ionică Bizău", "email": "<EMAIL>", "url": "https://ionicabizau.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/protocols", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/protocols/LICENSE"}, "proxy-addr@2.0.7": {"licenses": "MIT", "repository": "https://github.com/jshttp/proxy-addr", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-addr", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-addr/LICENSE"}, "proxy-agent@6.3.1": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-agent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-agent/README.md"}, "proxy-from-env@1.1.0": {"licenses": "MIT", "repository": "https://github.com/Rob--W/proxy-from-env", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://robwu.nl/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-from-env", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/proxy-from-env/LICENSE"}, "punycode@2.3.1": {"licenses": "MIT", "repository": "https://github.com/mathiasbynens/punycode.js", "publisher": "<PERSON>", "url": "https://mathiasbynens.be/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/punycode", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/punycode/LICENSE-MIT.txt"}, "pupa@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/pupa", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pupa", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pupa/license"}, "pure-rand@6.1.0": {"licenses": "MIT", "repository": "https://github.com/dubzzz/pure-rand", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pure-rand", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/pure-rand/LICENSE"}, "qs@6.11.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/ljharb/qs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/qs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/qs/LICENSE.md"}, "qs@6.13.0": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/ljharb/qs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/qs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/qs/LICENSE.md"}, "querystringify@2.2.0": {"licenses": "MIT", "repository": "https://github.com/unshiftio/querystringify", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/querystringify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/querystringify/LICENSE"}, "queue-microtask@1.2.3": {"licenses": "MIT", "repository": "https://github.com/feross/queue-microtask", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/queue-microtask", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/queue-microtask/LICENSE"}, "quick-lru@5.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/quick-lru", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/quick-lru", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/quick-lru/license"}, "randombytes@2.1.0": {"licenses": "MIT", "repository": "https://github.com/crypto-browserify/randombytes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/randombytes", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/randombytes/LICENSE"}, "range-parser@1.2.1": {"licenses": "MIT", "repository": "https://github.com/jshttp/range-parser", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://tjholowaychuk.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/range-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/range-parser/LICENSE"}, "raw-body@2.5.2": {"licenses": "MIT", "repository": "https://github.com/stream-utils/raw-body", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/raw-body", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/raw-body/LICENSE"}, "rc@1.2.8": {"licenses": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "repository": "https://github.com/dominictarr/rc", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rc", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rc/LICENSE.APACHE2"}, "react-is@18.3.1": {"licenses": "MIT", "repository": "https://github.com/facebook/react", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/react-is", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/react-is/LICENSE"}, "readable-stream@1.1.14": {"licenses": "MIT", "repository": "https://github.com/isaacs/readable-stream", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/readable-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/readable-stream/LICENSE"}, "readable-stream@2.3.8": {"licenses": "MIT", "repository": "https://github.com/nodejs/readable-stream", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream/node_modules/readable-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream/node_modules/readable-stream/LICENSE"}, "readable-stream@3.6.2": {"licenses": "MIT", "repository": "https://github.com/nodejs/readable-stream", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bl/node_modules/readable-stream", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bl/node_modules/readable-stream/LICENSE"}, "readdirp@3.6.0": {"licenses": "MIT", "repository": "https://github.com/paulmillr/readdirp", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "thlorenz.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/readdirp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/readdirp/LICENSE"}, "rechoir@0.6.2": {"licenses": "MIT", "repository": "https://github.com/tkellen/node-rechoir", "publisher": "<PERSON>", "url": "http://goingslowly.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rechoir", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rechoir/LICENSE"}, "reflect-metadata@0.1.14": {"licenses": "Apache-2.0", "repository": "https://github.com/rbuckton/reflect-metadata", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/rbuckton", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/reflect-metadata", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/reflect-metadata/LICENSE"}, "reflect.getprototypeof@1.0.10": {"licenses": "MIT", "repository": "https://github.com/es-shims/Reflect.getPrototypeOf", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/reflect.getprototypeof", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/reflect.getprototypeof/LICENSE"}, "regexp.prototype.flags@1.5.4": {"licenses": "MIT", "repository": "https://github.com/es-shims/RegExp.prototype.flags", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/regexp.prototype.flags", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/regexp.prototype.flags/LICENSE"}, "registry-auth-token@5.0.3": {"licenses": "MIT", "repository": "https://github.com/rexxars/registry-auth-token", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/registry-auth-token", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/registry-auth-token/LICENSE"}, "registry-url@6.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/registry-url", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/registry-url", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/registry-url/license"}, "release-it@16.3.0": {"licenses": "MIT", "repository": "https://github.com/release-it/release-it", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/LICENSE"}, "repeat-string@1.6.1": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/repeat-string", "publisher": "<PERSON>", "url": "http://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/repeat-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/repeat-string/LICENSE"}, "require-directory@2.1.1": {"licenses": "MIT", "repository": "https://github.com/troygoode/node-require-directory", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/require-directory", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/require-directory/LICENSE"}, "require-from-string@2.0.2": {"licenses": "MIT", "repository": "https://github.com/floatdrop/require-from-string", "publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/require-from-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/require-from-string/license"}, "requires-port@1.0.0": {"licenses": "MIT", "repository": "https://github.com/unshiftio/requires-port", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/requires-port", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/requires-port/LICENSE"}, "resolve-alpn@1.2.1": {"licenses": "MIT", "repository": "https://github.com/szmarczak/resolve-alpn", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve-alpn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve-alpn/LICENSE"}, "resolve-cwd@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/resolve-cwd", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve-cwd", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve-cwd/license"}, "resolve-from@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/resolve-from", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve-from", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve-from/license"}, "resolve-from@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/resolve-from", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/resolve-from", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/resolve-from/license"}, "resolve.exports@2.0.2": {"licenses": "MIT", "repository": "https://github.com/lukeed/resolve.exports", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve.exports", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve.exports/license"}, "resolve@1.22.8": {"licenses": "MIT", "repository": "https://github.com/browserify/resolve", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/resolve/LICENSE"}, "responselike@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/responselike", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukechilds.co.uk", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/responselike", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/responselike/license"}, "restore-cursor@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/restore-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/restore-cursor", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/restore-cursor/license"}, "restore-cursor@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/restore-cursor", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/restore-cursor", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/restore-cursor/license"}, "retry@0.13.1": {"licenses": "MIT", "repository": "https://github.com/tim-kos/node-retry", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/retry", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/retry/License"}, "reusify@1.0.4": {"licenses": "MIT", "repository": "https://github.com/mcollina/reusify", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/reusify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/reusify/LICENSE"}, "rimraf@3.0.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/rimraf", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rimraf", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rimraf/LICENSE"}, "rimraf@4.4.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/rimraf", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/rimraf", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/rimraf/LICENSE"}, "run-applescript@5.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/run-applescript", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/run-applescript", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/run-applescript/license"}, "run-async@2.4.1": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/run-async", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/run-async", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/run-async/LICENSE"}, "run-async@3.0.0": {"licenses": "MIT", "repository": "https://github.com/SBoudrias/run-async", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/run-async", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/run-async/LICENSE"}, "run-parallel@1.2.0": {"licenses": "MIT", "repository": "https://github.com/feross/run-parallel", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/run-parallel", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/run-parallel/LICENSE"}, "rxjs@7.8.1": {"licenses": "Apache-2.0", "repository": "https://github.com/reactivex/rxjs", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rxjs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rxjs/LICENSE.txt"}, "safe-array-concat@1.1.3": {"licenses": "MIT", "repository": "https://github.com/ljharb/safe-array-concat", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-array-concat", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-array-concat/LICENSE"}, "safe-buffer@5.1.2": {"licenses": "MIT", "repository": "https://github.com/feross/safe-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-buffer/LICENSE"}, "safe-buffer@5.2.1": {"licenses": "MIT", "repository": "https://github.com/feross/safe-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/content-disposition/node_modules/safe-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/content-disposition/node_modules/safe-buffer/LICENSE"}, "safe-push-apply@1.0.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/safe-push-apply", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-push-apply", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-push-apply/LICENSE"}, "safe-regex-test@1.1.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/safe-regex-test", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-regex-test", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-regex-test/LICENSE"}, "safe-stable-stringify@2.4.3": {"licenses": "MIT", "repository": "https://github.com/BridgeAR/safe-stable-stringify", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-stable-stringify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safe-stable-stringify/LICENSE"}, "safer-buffer@2.1.2": {"licenses": "MIT", "repository": "https://github.com/ChALkeR/safer-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/ChALkeR", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safer-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/safer-buffer/LICENSE"}, "schema-utils@3.3.0": {"licenses": "MIT", "repository": "https://github.com/webpack/schema-utils", "publisher": "webpack Contrib", "url": "https://github.com/webpack-contrib", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/schema-utils/LICENSE"}, "schema-utils@4.3.0": {"licenses": "MIT", "repository": "https://github.com/webpack/schema-utils", "publisher": "webpack Contrib", "url": "https://github.com/webpack-contrib", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser-webpack-plugin/node_modules/schema-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser-webpack-plugin/node_modules/schema-utils/LICENSE"}, "semver-diff@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/semver-diff", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/semver-diff", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/semver-diff/license"}, "semver@6.3.1": {"licenses": "ISC", "repository": "https://github.com/npm/node-semver", "publisher": "GitHub Inc.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-compilation-targets/node_modules/semver", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/helper-compilation-targets/node_modules/semver/LICENSE"}, "semver@7.5.4": {"licenses": "ISC", "repository": "https://github.com/npm/node-semver", "publisher": "GitHub Inc.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/semver", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/semver/LICENSE"}, "semver@7.6.2": {"licenses": "ISC", "repository": "https://github.com/npm/node-semver", "publisher": "GitHub Inc.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/semver", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/semver/LICENSE"}, "send@0.18.0": {"licenses": "MIT", "repository": "https://github.com/pillarjs/send", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/send", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/send/LICENSE"}, "send@0.19.0": {"licenses": "MIT", "repository": "https://github.com/pillarjs/send", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/send", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/send/LICENSE"}, "serialize-javascript@6.0.2": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/yahoo/serialize-javascript", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/serialize-javascript", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/serialize-javascript/LICENSE"}, "serve-static@1.15.0": {"licenses": "MIT", "repository": "https://github.com/expressjs/serve-static", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/serve-static", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/serve-static/LICENSE"}, "serve-static@1.16.2": {"licenses": "MIT", "repository": "https://github.com/expressjs/serve-static", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/serve-static", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@sw-web/nestjs-core/node_modules/serve-static/LICENSE"}, "set-function-length@1.2.2": {"licenses": "MIT", "repository": "https://github.com/ljharb/set-function-length", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/set-function-length", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/set-function-length/LICENSE"}, "set-function-name@2.0.2": {"licenses": "MIT", "repository": "https://github.com/ljharb/set-function-name", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/set-function-name", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/set-function-name/LICENSE"}, "set-proto@1.0.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/set-proto", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/set-proto", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/set-proto/LICENSE"}, "setprototypeof@1.2.0": {"licenses": "ISC", "repository": "https://github.com/wesleytodd/setprototypeof", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/setprototypeof", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/setprototypeof/LICENSE"}, "shebang-command@2.0.0": {"licenses": "MIT", "repository": "https://github.com/kevva/shebang-command", "publisher": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shebang-command", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shebang-command/license"}, "shebang-regex@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/shebang-regex", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shebang-regex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shebang-regex/license"}, "shelljs@0.8.5": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/shelljs/shelljs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/shelljs/LICENSE"}, "side-channel-list@1.0.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/side-channel-list", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel-list", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel-list/LICENSE"}, "side-channel-map@1.0.1": {"licenses": "MIT", "repository": "https://github.com/ljharb/side-channel-map", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel-map/LICENSE"}, "side-channel-weakmap@1.0.2": {"licenses": "MIT", "repository": "https://github.com/ljharb/side-channel-weakmap", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel-weakmap", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel-weakmap/LICENSE"}, "side-channel@1.1.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/side-channel", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/side-channel/LICENSE"}, "signal-exit@3.0.7": {"licenses": "ISC", "repository": "https://github.com/tapjs/signal-exit", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/restore-cursor/node_modules/signal-exit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/restore-cursor/node_modules/signal-exit/LICENSE.txt"}, "signal-exit@4.1.0": {"licenses": "ISC", "repository": "https://github.com/tapjs/signal-exit", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/signal-exit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/signal-exit/LICENSE.txt"}, "simple-swizzle@0.2.2": {"licenses": "MIT", "repository": "https://github.com/qix-/node-simple-swizzle", "publisher": "Qi<PERSON>", "url": "http://github.com/qix-", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/simple-swizzle", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/simple-swizzle/LICENSE"}, "sisteransi@1.0.5": {"licenses": "MIT", "repository": "https://github.com/terkelg/sisteransi", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://terkel.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/sisteransi", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/sisteransi/license"}, "slash@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/slash", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/slash", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/slash/license"}, "slash@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/slash", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/slash", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/slash/license"}, "smart-buffer@4.2.0": {"licenses": "MIT", "repository": "https://github.com/JoshGlazebrook/smart-buffer", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/smart-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/smart-buffer/LICENSE"}, "socks-proxy-agent@8.0.5": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/proxy-agents", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/socks-proxy-agent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/socks-proxy-agent/LICENSE"}, "socks@2.8.3": {"licenses": "MIT", "repository": "https://github.com/Josh<PERSON>lazebrook/socks", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/socks", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/socks/LICENSE"}, "source-map-support@0.5.13": {"licenses": "MIT", "repository": "https://github.com/evanw/node-source-map-support", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-runner/node_modules/source-map-support", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-runner/node_modules/source-map-support/LICENSE.md"}, "source-map-support@0.5.21": {"licenses": "MIT", "repository": "https://github.com/evanw/node-source-map-support", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/source-map-support", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/source-map-support/LICENSE.md"}, "source-map@0.6.1": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/mozilla/source-map", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escodegen/node_modules/source-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/escodegen/node_modules/source-map/LICENSE"}, "source-map@0.7.4": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/mozilla/source-map", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/source-map", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/source-map/LICENSE"}, "split2@4.2.0": {"licenses": "ISC", "repository": "https://github.com/mcollina/split2", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/split2", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/split2/LICENSE"}, "sprintf-js@1.0.3": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/alexei/sprintf.js", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://alexei.ro/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/sprintf-js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@istanbuljs/load-nyc-config/node_modules/sprintf-js/LICENSE"}, "sprintf-js@1.1.3": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/alexei/sprintf.js", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/sprintf-js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/sprintf-js/LICENSE"}, "stack-trace@0.0.10": {"licenses": "MIT", "repository": "https://github.com/felixge/node-stack-trace", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stack-trace", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stack-trace/License"}, "stack-utils@2.0.6": {"licenses": "MIT", "repository": "https://github.com/tapjs/stack-utils", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stack-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stack-utils/LICENSE.md"}, "statuses@2.0.1": {"licenses": "MIT", "repository": "https://github.com/jshttp/statuses", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/statuses", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/statuses/LICENSE"}, "stdin-discarder@0.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/stdin-discarder", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stdin-discarder", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stdin-discarder/license"}, "stop-iteration-iterator@1.1.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/stop-iteration-iterator", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stop-iteration-iterator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/stop-iteration-iterator/LICENSE"}, "streamsearch@1.1.0": {"licenses": "MIT", "repository": "https://github.com/mscdex/streamsearch", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/streamsearch", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/streamsearch/LICENSE"}, "string-length@4.0.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/string-length", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string-length", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string-length/license"}, "string-width@4.2.3": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/string-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string-width", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string-width/license"}, "string-width@5.1.2": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/string-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/string-width", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/string-width/license"}, "string-width@6.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/string-width", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/string-width", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/string-width/license"}, "string.prototype.trim@1.2.10": {"licenses": "MIT", "repository": "https://github.com/es-shims/String.prototype.trim", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string.prototype.trim", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string.prototype.trim/LICENSE"}, "string.prototype.trimend@1.0.9": {"licenses": "MIT", "repository": "https://github.com/es-shims/String.prototype.trimEnd", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string.prototype.trimend", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string.prototype.trimend/LICENSE"}, "string.prototype.trimstart@1.0.8": {"licenses": "MIT", "repository": "https://github.com/es-shims/String.prototype.trimStart", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string.prototype.trimstart", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string.prototype.trimstart/LICENSE"}, "string_decoder@0.10.31": {"licenses": "MIT", "repository": "https://github.com/rvagg/string_decoder", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string_decoder", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/string_decoder/LICENSE"}, "string_decoder@1.1.1": {"licenses": "MIT", "repository": "https://github.com/nodejs/string_decoder", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream/node_modules/string_decoder", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/concat-stream/node_modules/string_decoder/LICENSE"}, "string_decoder@1.3.0": {"licenses": "MIT", "repository": "https://github.com/nodejs/string_decoder", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bl/node_modules/string_decoder", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/bl/node_modules/string_decoder/LICENSE"}, "strip-ansi@6.0.1": {"licenses": "MIT", "repository": "https://github.com/chalk/strip-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-ansi", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-ansi/license"}, "strip-ansi@7.1.0": {"licenses": "MIT", "repository": "https://github.com/chalk/strip-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/strip-ansi", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/ora/node_modules/strip-ansi/license"}, "strip-bom@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-bom", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsconfig-paths/node_modules/strip-bom", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsconfig-paths/node_modules/strip-bom/license"}, "strip-bom@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-bom", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-bom", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-bom/license"}, "strip-final-newline@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-final-newline", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-final-newline", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-final-newline/license"}, "strip-final-newline@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-final-newline", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/strip-final-newline", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/strip-final-newline/license"}, "strip-json-comments@2.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-json-comments", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rc/node_modules/strip-json-comments", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/rc/node_modules/strip-json-comments/license"}, "strip-json-comments@3.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/strip-json-comments", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-json-comments", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/strip-json-comments/license"}, "subscriptions-transport-ws@0.11.0": {"licenses": "MIT", "repository": "https://github.com/apollostack/subscriptions-transport-ws", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/subscriptions-transport-ws", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/subscriptions-transport-ws/LICENSE"}, "superagent@8.1.2": {"licenses": "MIT", "repository": "https://github.com/ladjs/superagent", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/superagent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/superagent/LICENSE"}, "supertest@6.3.4": {"licenses": "MIT", "repository": "https://github.com/ladjs/supertest", "publisher": "<PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/supertest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/supertest/LICENSE"}, "supports-color@5.5.0": {"licenses": "MIT", "repository": "https://github.com/chalk/supports-color", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/supports-color", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@babel/highlight/node_modules/supports-color/license"}, "supports-color@7.2.0": {"licenses": "MIT", "repository": "https://github.com/chalk/supports-color", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/supports-color", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/supports-color/license"}, "supports-color@8.1.1": {"licenses": "MIT", "repository": "https://github.com/chalk/supports-color", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-worker/node_modules/supports-color", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/jest-worker/node_modules/supports-color/license"}, "supports-preserve-symlinks-flag@1.0.0": {"licenses": "MIT", "repository": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/supports-preserve-symlinks-flag", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/supports-preserve-symlinks-flag/LICENSE"}, "swagger-ui-dist@5.11.2": {"licenses": "Apache-2.0", "repository": "https://github.com/swagger-api/swagger-ui", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/swagger-ui-dist", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/swagger-ui-dist/LICENSE", "noticeFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/swagger-ui-dist/NOTICE"}, "symbol-observable@1.2.0": {"licenses": "MIT", "repository": "https://github.com/blesh/symbol-observable", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/subscriptions-transport-ws/node_modules/symbol-observable", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/subscriptions-transport-ws/node_modules/symbol-observable/license"}, "symbol-observable@4.0.0": {"licenses": "MIT", "repository": "https://github.com/blesh/symbol-observable", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/symbol-observable", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/symbol-observable/license"}, "synckit@0.8.8": {"licenses": "MIT", "repository": "https://github.com/un-ts/synckit", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.1stG.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/synckit", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/synckit/LICENSE"}, "tapable@2.2.1": {"licenses": "MIT", "repository": "https://github.com/webpack/tapable", "publisher": "<PERSON> @sokra", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tapable", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tapable/LICENSE"}, "terser-webpack-plugin@5.3.14": {"licenses": "MIT", "repository": "https://github.com/webpack-contrib/terser-webpack-plugin", "publisher": "webpack Contrib Team", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser-webpack-plugin", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser-webpack-plugin/LICENSE"}, "terser@5.39.0": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/terser/terser", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://lisperator.net/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/terser/LICENSE"}, "test-exclude@6.0.0": {"licenses": "ISC", "repository": "https://github.com/istanbuljs/test-exclude", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/test-exclude", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/test-exclude/LICENSE.txt"}, "text-hex@1.0.0": {"licenses": "MIT", "repository": "https://github.com/3rd-Eden/text-hex", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/text-hex", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/text-hex/LICENSE"}, "text-table@0.2.0": {"licenses": "MIT", "repository": "https://github.com/substack/text-table", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/text-table", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/text-table/LICENSE"}, "through@2.3.8": {"licenses": "MIT", "repository": "https://github.com/dominictarr/through", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "dominictarr.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/through", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/through/LICENSE.APACHE2"}, "titleize@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/titleize", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/titleize", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/titleize/license"}, "tmp@0.0.33": {"licenses": "MIT", "repository": "https://github.com/raszi/node-tmp", "publisher": "KARASZI István", "email": "<EMAIL>", "url": "http://raszi.hu/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tmp", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tmp/LICENSE"}, "tmpl@1.0.5": {"licenses": "BSD-3-<PERSON><PERSON>", "repository": "https://github.com/daaku/nodejs-tmpl", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tmpl", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tmpl/license"}, "to-fast-properties@2.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/to-fast-properties", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/to-fast-properties", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/to-fast-properties/license"}, "to-regex-range@5.0.1": {"licenses": "MIT", "repository": "https://github.com/micromatch/to-regex-range", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/to-regex-range", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/to-regex-range/LICENSE"}, "toidentifier@1.0.1": {"licenses": "MIT", "repository": "https://github.com/component/toidentifier", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/toidentifier", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/toidentifier/LICENSE"}, "tr46@0.0.3": {"licenses": "MIT", "repository": "https://github.com/Sebmaster/tr46.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tr46"}, "tree-kill@1.2.2": {"licenses": "MIT", "repository": "https://github.com/pkrumins/node-tree-kill", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.catonmat.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tree-kill", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tree-kill/LICENSE"}, "triple-beam@1.4.1": {"licenses": "MIT", "repository": "https://github.com/winstonjs/triple-beam", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/triple-beam", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/triple-beam/LICENSE"}, "ts-api-utils@1.3.0": {"licenses": "MIT", "repository": "https://github.com/JoshuaKGoldberg/ts-api-utils", "publisher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-api-utils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-api-utils/LICENSE.md"}, "ts-jest@29.1.2": {"licenses": "MIT", "repository": "https://github.com/kulshekhar/ts-jest", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "email": "k<PERSON><PERSON><PERSON>@users.noreply.github.com", "url": "https://github.com/kul<PERSON>khar", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-jest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-jest/LICENSE.md"}, "ts-loader@9.5.1": {"licenses": "MIT", "repository": "https://github.com/TypeStrong/ts-loader", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://johnnyreilly.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-loader", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-loader/LICENSE"}, "ts-node@10.9.2": {"licenses": "MIT", "repository": "https://github.com/TypeStrong/ts-node", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-node", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ts-node/LICENSE"}, "tsconfig-paths-webpack-plugin@4.1.0": {"licenses": "MIT", "repository": "https://github.com/dividab/tsconfig-paths-webpack-plugin", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsconfig-paths-webpack-plugin", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsconfig-paths-webpack-plugin/LICENSE"}, "tsconfig-paths@3.15.0": {"licenses": "MIT", "repository": "https://github.com/dividab/tsconfig-paths", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import/node_modules/tsconfig-paths", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/eslint-plugin-import/node_modules/tsconfig-paths/LICENSE"}, "tsconfig-paths@4.2.0": {"licenses": "MIT", "repository": "https://github.com/dividab/tsconfig-paths", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsconfig-paths", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsconfig-paths/LICENSE"}, "tslib@1.14.1": {"licenses": "0BSD", "repository": "https://github.com/Microsoft/tslib", "publisher": "Microsoft Corp.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsutils/node_modules/tslib", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsutils/node_modules/tslib/LICENSE.txt"}, "tslib@2.6.2": {"licenses": "0BSD", "repository": "https://github.com/Microsoft/tslib", "publisher": "Microsoft Corp.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tslib", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tslib/LICENSE.txt"}, "tsutils@3.21.0": {"licenses": "MIT", "repository": "https://github.com/ajafff/tsutils", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsutils", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/tsutils/LICENSE"}, "type-check@0.4.0": {"licenses": "MIT", "repository": "https://github.com/gkz/type-check", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-check", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-check/LICENSE"}, "type-detect@4.0.8": {"licenses": "MIT", "repository": "https://github.com/chaijs/type-detect", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://alogicalparadox.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-detect", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-detect/LICENSE"}, "type-fest@0.20.2": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-fest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-fest/license"}, "type-fest@0.21.3": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-escapes/node_modules/type-fest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ansi-escapes/node_modules/type-fest/license"}, "type-fest@1.4.0": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/crypto-random-string/node_modules/type-fest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/crypto-random-string/node_modules/type-fest/license"}, "type-fest@2.19.0": {"licenses": "(MIT OR CC0-1.0)", "repository": "https://github.com/sindresorhus/type-fest", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/new-github-release-url/node_modules/type-fest", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/new-github-release-url/node_modules/type-fest/readme.md"}, "type-is@1.6.18": {"licenses": "MIT", "repository": "https://github.com/jshttp/type-is", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-is", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/type-is/LICENSE"}, "typed-array-buffer@1.0.3": {"licenses": "MIT", "repository": "https://github.com/inspect-js/typed-array-buffer", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-buffer/LICENSE"}, "typed-array-byte-length@1.0.3": {"licenses": "MIT", "repository": "https://github.com/inspect-js/typed-array-byte-length", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-byte-length", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-byte-length/LICENSE"}, "typed-array-byte-offset@1.0.4": {"licenses": "MIT", "repository": "https://github.com/inspect-js/typed-array-byte-offset", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-byte-offset", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-byte-offset/LICENSE"}, "typed-array-length@1.0.7": {"licenses": "MIT", "repository": "https://github.com/inspect-js/typed-array-length", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-length", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typed-array-length/LICENSE"}, "typedarray-to-buffer@3.1.5": {"licenses": "MIT", "repository": "https://github.com/feross/typedarray-to-buffer", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://feross.org/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typedarray-to-buffer", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typedarray-to-buffer/LICENSE"}, "typedarray@0.0.6": {"licenses": "MIT", "repository": "https://github.com/substack/typedarray", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typedarray", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typedarray/LICENSE"}, "typescript@5.3.3": {"licenses": "Apache-2.0", "repository": "https://github.com/Microsoft/TypeScript", "publisher": "Microsoft Corp.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/typescript", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/typescript/LICENSE.txt"}, "typescript@5.4.5": {"licenses": "Apache-2.0", "repository": "https://github.com/Microsoft/TypeScript", "publisher": "Microsoft Corp.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typescript", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/typescript/LICENSE.txt"}, "ua-parser-js@1.0.39": {"licenses": "MIT", "repository": "https://github.com/faisalman/ua-parser-js", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://faisalman.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ua-parser-js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ua-parser-js/license.md"}, "uid@2.0.2": {"licenses": "MIT", "repository": "https://github.com/lukeed/uid", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/uid", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/uid/license"}, "ulid@2.3.0": {"licenses": "MIT", "repository": "https://github.com/ulid/javascript", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ulid", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ulid/LICENSE"}, "unbox-primitive@1.1.0": {"licenses": "MIT", "repository": "https://github.com/ljharb/unbox-primitive", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/unbox-primitive", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/unbox-primitive/LICENSE"}, "undici-types@5.26.5": {"licenses": "MIT", "repository": "https://github.com/nodejs/undici", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/undici-types", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/undici-types/README.md"}, "unique-string@3.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/unique-string", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/unique-string", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/unique-string/license"}, "universal-user-agent@6.0.1": {"licenses": "ISC", "repository": "https://github.com/gr2m/universal-user-agent", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/universal-user-agent", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/universal-user-agent/LICENSE.md"}, "universalify@2.0.1": {"licenses": "MIT", "repository": "https://github.com/RyanZim/universalify", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/universalify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/universalify/LICENSE"}, "unpipe@1.0.0": {"licenses": "MIT", "repository": "https://github.com/stream-utils/unpipe", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/unpipe", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/unpipe/LICENSE"}, "untildify@4.0.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/untildify", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/untildify", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/untildify/license"}, "update-browserslist-db@1.1.3": {"licenses": "MIT", "repository": "https://github.com/browserslist/update-db", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-browserslist-db", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-browserslist-db/LICENSE"}, "update-notifier@6.0.2": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/yeoman/update-notifier", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/license"}, "uri-js@4.4.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/garycourt/uri-js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/uri-js", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/uri-js/LICENSE"}, "url-join@5.0.0": {"licenses": "MIT", "repository": "https://github.com/jfromaniello/url-join", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://joseoncode.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/url-join", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/url-join/LICENSE"}, "url-parse@1.5.10": {"licenses": "MIT", "repository": "https://github.com/unshiftio/url-parse", "publisher": "<PERSON><PERSON><PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/url-parse", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/url-parse/LICENSE"}, "util-deprecate@1.0.2": {"licenses": "MIT", "repository": "https://github.com/TooTallNate/util-deprecate", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://n8.io/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/util-deprecate", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/util-deprecate/LICENSE"}, "utils-merge@1.0.1": {"licenses": "MIT", "repository": "https://github.com/jaredhanson/utils-merge", "publisher": "<PERSON>", "email": "jared<PERSON><PERSON>@gmail.com", "url": "http://www.jaredhanson.net/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/utils-merge", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/utils-merge/LICENSE"}, "uuid@9.0.1": {"licenses": "MIT", "repository": "https://github.com/uuidjs/uuid", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/uuid", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/uuid/LICENSE.md"}, "v8-compile-cache-lib@3.0.1": {"licenses": "MIT", "repository": "https://github.com/cspotcode/v8-compile-cache-lib", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/v8-compile-cache-lib", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/v8-compile-cache-lib/LICENSE"}, "v8-to-istanbul@9.2.0": {"licenses": "ISC", "repository": "https://github.com/istanbuljs/v8-to-istanbul", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/v8-to-istanbul", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/v8-to-istanbul/LICENSE.txt"}, "validator@13.12.0": {"licenses": "MIT", "repository": "https://github.com/validatorjs/validator.js", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/validator", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/validator/LICENSE"}, "value-or-promise@1.0.12": {"licenses": "MIT", "repository": "https://github.com/yaacovCR/value-or-promise", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/value-or-promise", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/value-or-promise/LICENSE"}, "vary@1.1.2": {"licenses": "MIT", "repository": "https://github.com/jshttp/vary", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/vary", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/vary/LICENSE"}, "walker@1.0.8": {"licenses": "Apache-2.0", "repository": "https://github.com/daaku/nodejs-walker", "publisher": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/walker", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/walker/LICENSE"}, "watchpack@2.4.2": {"licenses": "MIT", "repository": "https://github.com/webpack/watchpack", "publisher": "<PERSON> @sokra", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/watchpack", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/watchpack/LICENSE"}, "wcwidth@1.0.1": {"licenses": "MIT", "repository": "https://github.com/timoxley/wcwidth", "publisher": "<PERSON>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wcwidth", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wcwidth/LICENSE"}, "web-streams-polyfill@3.3.3": {"licenses": "MIT", "repository": "https://github.com/MattiasBuelens/web-streams-polyfill", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/web-streams-polyfill", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/web-streams-polyfill/LICENSE"}, "webidl-conversions@3.0.1": {"licenses": "BSD-2-<PERSON><PERSON>", "repository": "https://github.com/jsdom/webidl-conversions", "publisher": "Domenic <PERSON>", "email": "<EMAIL>", "url": "https://domenic.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webidl-conversions", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webidl-conversions/LICENSE.md"}, "webpack-node-externals@3.0.0": {"licenses": "MIT", "repository": "https://github.com/liady/webpack-node-externals", "publisher": "<PERSON><PERSON>", "url": "https://github.com/liady", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webpack-node-externals", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webpack-node-externals/LICENSE"}, "webpack-sources@3.2.3": {"licenses": "MIT", "repository": "https://github.com/webpack/webpack-sources", "publisher": "<PERSON> @sokra", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webpack-sources", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webpack-sources/LICENSE"}, "webpack@5.90.1": {"licenses": "MIT", "repository": "https://github.com/webpack/webpack", "publisher": "<PERSON> @sokra", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/webpack", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/@nestjs/cli/node_modules/webpack/LICENSE"}, "webpack@5.99.6": {"licenses": "MIT", "repository": "https://github.com/webpack/webpack", "publisher": "<PERSON> @sokra", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webpack", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/webpack/LICENSE"}, "whatwg-url@5.0.0": {"licenses": "MIT", "repository": "https://github.com/jsdom/whatwg-url", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/whatwg-url", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/whatwg-url/LICENSE.txt"}, "which-boxed-primitive@1.1.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/which-boxed-primitive", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-boxed-primitive", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-boxed-primitive/LICENSE"}, "which-builtin-type@1.2.1": {"licenses": "MIT", "repository": "https://github.com/inspect-js/which-builtin-type", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-builtin-type", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-builtin-type/LICENSE"}, "which-collection@1.0.2": {"licenses": "MIT", "repository": "https://github.com/inspect-js/which-collection", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-collection", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-collection/LICENSE"}, "which-typed-array@1.1.18": {"licenses": "MIT", "repository": "https://github.com/inspect-js/which-typed-array", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-typed-array", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which-typed-array/LICENSE"}, "which@2.0.2": {"licenses": "ISC", "repository": "https://github.com/isaacs/node-which", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/which/LICENSE"}, "widest-line@3.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/widest-line", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/widest-line", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/widest-line/license"}, "widest-line@4.0.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/widest-line", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/widest-line", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/widest-line/license"}, "wildcard-match@5.1.2": {"licenses": "ISC", "repository": "https://github.com/axtgr/wildcard-match", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wildcard-match", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wildcard-match/LICENSE"}, "windows-release@5.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/windows-release", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/windows-release", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/windows-release/license"}, "winston-transport@4.7.0": {"licenses": "MIT", "repository": "https://github.com/winstonjs/winston-transport", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/winston-transport", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/winston-transport/LICENSE"}, "winston@3.13.0": {"licenses": "MIT", "repository": "https://github.com/winstonjs/winston", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/winston", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/winston/LICENSE"}, "word-wrap@1.2.5": {"licenses": "MIT", "repository": "https://github.com/jonschlinkert/word-wrap", "publisher": "<PERSON>", "url": "https://github.com/jonschlinkert", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/word-wrap", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/word-wrap/LICENSE"}, "wrap-ansi@6.2.0": {"licenses": "MIT", "repository": "https://github.com/chalk/wrap-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inquirer/node_modules/wrap-ansi", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/inquirer/node_modules/wrap-ansi/license"}, "wrap-ansi@7.0.0": {"licenses": "MIT", "repository": "https://github.com/chalk/wrap-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wrap-ansi", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wrap-ansi/license"}, "wrap-ansi@8.1.0": {"licenses": "MIT", "repository": "https://github.com/chalk/wrap-ansi", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/wrap-ansi", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/update-notifier/node_modules/wrap-ansi/license"}, "wrappy@1.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/wrappy", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wrappy", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/wrappy/LICENSE"}, "write-file-atomic@3.0.3": {"licenses": "ISC", "repository": "https://github.com/npm/write-file-atomic", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://re-becca.org", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/configstore/node_modules/write-file-atomic", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/configstore/node_modules/write-file-atomic/LICENSE"}, "write-file-atomic@4.0.2": {"licenses": "ISC", "repository": "https://github.com/npm/write-file-atomic", "publisher": "GitHub Inc.", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/write-file-atomic", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/write-file-atomic/LICENSE.md"}, "ws@7.5.9": {"licenses": "MIT", "repository": "https://github.com/websockets/ws", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/subscriptions-transport-ws/node_modules/ws", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/subscriptions-transport-ws/node_modules/ws/LICENSE"}, "ws@8.14.2": {"licenses": "MIT", "repository": "https://github.com/websockets/ws", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://2x.io", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ws", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/ws/LICENSE"}, "xdg-basedir@5.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/xdg-basedir", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/xdg-basedir", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/xdg-basedir/license"}, "xtend@4.0.2": {"licenses": "MIT", "repository": "https://github.com/Raynos/xtend", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/xtend", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/xtend/LICENSE"}, "y18n@5.0.8": {"licenses": "ISC", "repository": "https://github.com/yargs/y18n", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/y18n", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/y18n/LICENSE"}, "yallist@3.1.1": {"licenses": "ISC", "repository": "https://github.com/isaacs/yallist", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yallist", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yallist/LICENSE"}, "yallist@4.0.0": {"licenses": "ISC", "repository": "https://github.com/isaacs/yallist", "publisher": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/yallist", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/release-it/node_modules/yallist/LICENSE"}, "yargs-parser@21.1.1": {"licenses": "ISC", "repository": "https://github.com/yargs/yargs-parser", "publisher": "<PERSON>", "email": "<EMAIL>", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yargs-parser", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yargs-parser/LICENSE.txt"}, "yargs@17.7.2": {"licenses": "MIT", "repository": "https://github.com/yargs/yargs", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yargs", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yargs/LICENSE"}, "yn@3.1.1": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/yn", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yn", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yn/license"}, "yocto-queue@0.1.0": {"licenses": "MIT", "repository": "https://github.com/sindresorhus/yocto-queue", "publisher": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com", "path": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yocto-queue", "licenseFile": "/Users/<USER>/Documents/itrdev/sales-hub/node_modules/yocto-queue/license"}}