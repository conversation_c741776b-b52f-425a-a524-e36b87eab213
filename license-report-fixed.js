const fs = require('fs');
const path = require('path');

// Read the licenses.json file
const licensesData = require('./licenses.json');

// Function to count licenses by type
function countLicensesByType(data) {
  const licenseCount = {};

  Object.values(data).forEach(item => {
    const license = item.licenses || 'Unknown';
    licenseCount[license] = (licenseCount[license] || 0) + 1;
  });

  return licenseCount;
}

// Define risk levels for common licenses
function getLicenseRiskLevel(licenseType) {
  // Moved Apache-2.0 to low risk category as it's generally business-friendly
  const lowRisk = ['MIT', 'ISC', 'BSD', 'BSD-2-Clause', 'BSD-3-Clause', 'CC0-1.0', '0BSD', 'Unlicense', 'Apache-2.0'];
  const mediumRisk = ['MPL-2.0', 'EPL-1.0', 'EPL-2.0', 'LGPL', 'LGPL-2.1', 'LGPL-3.0'];
  const highRisk = ['GPL', 'GPL-2.0', 'GPL-3.0', 'AGPL', 'AGPL-3.0', 'CDDL', 'EUPL', 'EUPL-1.1'];

  if (lowRisk.some(l => licenseType.includes(l))) return 'Low';
  if (mediumRisk.some(l => licenseType.includes(l))) return 'Medium';
  if (highRisk.some(l => licenseType.includes(l))) return 'High';

  return 'Unknown';
}

// Generate license distribution data
function generateLicenseDistribution(licenseCount) {
  const distribution = [];

  for (const [type, count] of Object.entries(licenseCount)) {
    const riskLevel = getLicenseRiskLevel(type);
    distribution.push({
      type,
      count,
      risk_level: riskLevel,
      risk_class: riskLevel.toLowerCase() + '-risk'
    });
  }

  // Sort by count (descending)
  return distribution.sort((a, b) => b.count - a.count);
}

// Generate license categories data
function generateLicenseCategories() {
  return [
    {
      level: 'Low',
      description: 'Low-risk licenses are permissive and impose minimal restrictions on software usage, modification, and distribution. They typically allow for commercial use with few obligations.',
      licenses: [
        {
          name: 'MIT',
          permissions: 'Commercial use, modification, distribution, private use',
          limitations: 'No liability, no warranty',
          conditions: 'License and copyright notice must be included',
          implications: 'Very permissive license with minimal requirements. Safe for most commercial applications.'
        },
        {
          name: 'Apache-2.0',
          permissions: 'Commercial use, modification, distribution, patent use, private use',
          limitations: 'No liability, no trademark use, no warranty',
          conditions: 'License and copyright notice, state changes, patent grant',
          implications: 'Business-friendly license that explicitly grants patent rights. Requires documentation of changes but doesn\'t require code sharing.'
        },
        {
          name: 'ISC',
          permissions: 'Commercial use, modification, distribution, private use',
          limitations: 'No liability, no warranty',
          conditions: 'License and copyright notice must be included',
          implications: 'Similar to MIT, very permissive with minimal requirements.'
        },
        {
          name: 'BSD-3-Clause',
          permissions: 'Commercial use, modification, distribution, private use',
          limitations: 'No liability, no warranty',
          conditions: 'License and copyright notice must be included, cannot use contributors\' names for promotion',
          implications: 'Permissive license with minimal requirements, safe for commercial use.'
        }
      ]
    },
    {
      level: 'Medium',
      description: 'Medium-risk licenses include reciprocal or copyleft provisions that may require sharing modifications under certain conditions. They generally allow commercial use but have more specific requirements.',
      licenses: [
        {
          name: 'MPL-2.0',
          permissions: 'Commercial use, modification, distribution, patent use, private use',
          limitations: 'No liability, no warranty',
          conditions: 'License and copyright notice, same license (file-level), disclose source',
          implications: 'Weak copyleft license that requires modifications to specific files to be shared, but allows linking with proprietary code.'
        },
        {
          name: 'LGPL-3.0',
          permissions: 'Commercial use, modification, distribution, patent use, private use',
          limitations: 'No liability, no warranty',
          conditions: 'License and copyright notice, same license (library), disclose source, state changes',
          implications: 'Allows linking with proprietary software but modifications to the library itself must be shared.'
        }
      ]
    },
    {
      level: 'High',
      description: 'High-risk licenses contain strong copyleft provisions that may require sharing all source code of the entire application. These can significantly impact commercial software distribution and may require legal review.',
      licenses: [
        {
          name: 'GPL-3.0',
          permissions: 'Commercial use, modification, distribution, patent use, private use',
          limitations: 'No liability, no warranty',
          conditions: 'License and copyright notice, same license, disclose source, state changes',
          implications: 'Strong copyleft license that requires all derivative works to be distributed under the same license. May require sharing your entire application source code.'
        },
        {
          name: 'AGPL-3.0',
          permissions: 'Commercial use, modification, distribution, patent use, private use',
          limitations: 'No liability, no warranty',
          conditions: 'License and copyright notice, same license, disclose source, state changes, network use is distribution',
          implications: 'Strongest copyleft license that considers network use as distribution. Using AGPL code in a web service requires making the entire source code available.'
        }
      ]
    }
  ];
}

// Identify packages with potential issues
function identifyPotentialIssues(data) {
  const highRiskPackages = [];
  const mediumRiskPackages = [];

  Object.entries(data).forEach(([name, item]) => {
    const license = item.licenses || 'Unknown';
    const riskLevel = getLicenseRiskLevel(license);

    if (riskLevel === 'High') {
      highRiskPackages.push({
        name: name,
        version: name.split('@').pop(),
        license: license,
        usage: 'Direct dependency',
        risk_level: 'High',
        issue: 'Strong copyleft license may require sharing all source code',
        recommendation: 'Consider replacing with an alternative library or consult legal team'
      });
    } else if (riskLevel === 'Medium') {
      mediumRiskPackages.push({
        name: name,
        version: name.split('@').pop(),
        license: license,
        usage: 'Direct dependency',
        risk_level: 'Medium',
        issue: 'Weak copyleft provisions may require sharing some modifications',
        recommendation: 'Review license terms for compliance requirements'
      });
    }
  });

  return {
    high: highRiskPackages,
    medium: mediumRiskPackages
  };
}

// Generate conclusion and recommendations
function generateConclusion(licenseDistribution) {
  const totalPackages = licenseDistribution.reduce((sum, item) => sum + item.count, 0);
  const lowRiskCount = licenseDistribution.filter(item => item.risk_level === 'Low').reduce((sum, item) => sum + item.count, 0);
  const mediumRiskCount = licenseDistribution.filter(item => item.risk_level === 'Medium').reduce((sum, item) => sum + item.count, 0);
  const highRiskCount = licenseDistribution.filter(item => item.risk_level === 'High').reduce((sum, item) => sum + item.count, 0);

  const lowRiskPercentage = Math.round((lowRiskCount / totalPackages) * 100);
  const mediumRiskPercentage = Math.round((mediumRiskCount / totalPackages) * 100);
  const highRiskPercentage = Math.round((highRiskCount / totalPackages) * 100);

  return {
    intro: `This project uses ${totalPackages} third-party packages with various licenses. ${lowRiskPercentage}% of packages use low-risk licenses, ${mediumRiskPercentage}% use medium-risk licenses, and ${highRiskPercentage}% use high-risk licenses.`,
    points: [
      `The majority of dependencies (${lowRiskCount} packages, ${lowRiskPercentage}%) use permissive licenses like MIT and ISC, which pose minimal risk for commercial use.`,
      `${mediumRiskCount} packages (${mediumRiskPercentage}%) use licenses with moderate restrictions, primarily Apache-2.0, which may require additional compliance measures.`,
      `${highRiskCount} packages (${highRiskPercentage}%) use licenses with strong copyleft provisions that may require careful review.`
    ],
    recommendations: [
      'Review and address any high-risk dependencies identified in this report.',
      'Establish a process for license compliance review before adding new dependencies.',
      'Consider creating an approved license list for future development.',
      'Maintain documentation of all third-party components and their licenses.',
      'Consult with legal counsel for specific guidance on license compliance for your commercial products.'
    ]
  };
}

// Generate CSV data
function generateCSV(data) {
  const header = 'Package Name,Version,License,Risk Level\n';
  const rows = Object.entries(data).map(([name, item]) => {
    // Extract package name and version from the full name
    let packageName = '';
    let version = '';

    if (name.startsWith('@')) {
      // Scoped package like @nestjs/common@10.3.8
      const parts = name.split('@');
      packageName = '@' + parts[1]; // @nestjs/common
      version = parts[2] || ''; // 10.3.8
    } else {
      // Regular package like lodash@4.17.21
      const parts = name.split('@');
      packageName = parts[0]; // lodash
      version = parts[1] || ''; // 4.17.21
    }

    const license = item.licenses || 'Unknown';
    const riskLevel = getLicenseRiskLevel(license);

    return `"${packageName}","${version}","${license}","${riskLevel}"`;
  });

  return header + rows.join('\n');
}

// Generate HTML report
function generateHTML() {
  // Read the HTML template
  const templatePath = './template.html';
  let template = fs.readFileSync(templatePath, 'utf8');

  // Process data
  const licenseCount = countLicensesByType(licensesData);
  const licenseDistribution = generateLicenseDistribution(licenseCount);
  const licenseCategories = generateLicenseCategories();
  const potentialIssues = identifyPotentialIssues(licensesData);
  const conclusion = generateConclusion(licenseDistribution);

  // Create HTML content
  let html = template;

  // Replace title and intro
  html = html.replace(/{{report_title}}/g, 'Third-Party Software License Report');
  html = html.replace(/{{report_intro}}/g, 'This report provides an analysis of third-party software licenses used in this project, including their distribution, implications, and potential compliance issues for commercial use.');

  // Replace license distribution table
  let distributionRows = '';
  licenseDistribution.forEach(item => {
    distributionRows += `<tr>
      <td>${item.type}</td>
      <td>${item.count}</td>
      <td class="${item.risk_class}">${item.risk_level}</td>
    </tr>`;
  });
  html = html.replace(/{{#each license_distribution}}[\s\S]*?{{\/each}}/g, distributionRows);

  // Replace license categories
  let categoriesHtml = '';
  licenseCategories.forEach(category => {
    categoriesHtml += `<h3>${category.level}-Risk Licenses</h3>
    <p>${category.description}</p>`;

    category.licenses.forEach(license => {
      categoriesHtml += `<h4>${license.name}</h4>
      <ul>
        <li><strong>Permissions</strong>: ${license.permissions}</li>
        <li><strong>Limitations</strong>: ${license.limitations}</li>
        <li><strong>Conditions</strong>: ${license.conditions}</li>
        <li><strong>Commercial Implications</strong>: ${license.implications}</li>
      </ul>`;
    });
  });
  html = html.replace(/{{#each license_categories}}[\s\S]*?{{\/each}}/g, categoriesHtml);

  // Replace risk packages
  let riskPackagesHtml = '';
  if (potentialIssues.high.length > 0) {
    riskPackagesHtml += `<h3>High-Risk Packages</h3>`;
    potentialIssues.high.forEach((pkg, index) => {
      riskPackagesHtml += `<h4>${index+1}. ${pkg.name} (${pkg.license})</h4>
      <ul>
        <li><strong>Version</strong>: ${pkg.version}</li>
        <li><strong>Usage</strong>: ${pkg.usage}</li>
        <li><strong>Risk</strong>: ${pkg.risk_level}</li>
        <li><strong>Issue</strong>: ${pkg.issue}</li>
        <li><strong>Recommendation</strong>: ${pkg.recommendation}</li>
      </ul>`;
    });
  }

  if (potentialIssues.medium.length > 0) {
    riskPackagesHtml += `<h3>Medium-Risk Packages</h3>`;
    potentialIssues.medium.forEach((pkg, index) => {
      riskPackagesHtml += `<h4>${index+1}. ${pkg.name} (${pkg.license})</h4>
      <ul>
        <li><strong>Version</strong>: ${pkg.version}</li>
        <li><strong>Usage</strong>: ${pkg.usage}</li>
        <li><strong>Risk</strong>: ${pkg.risk_level}</li>
        <li><strong>Issue</strong>: ${pkg.issue}</li>
        <li><strong>Recommendation</strong>: ${pkg.recommendation}</li>
      </ul>`;
    });
  }

  if (potentialIssues.high.length === 0 && potentialIssues.medium.length === 0) {
    riskPackagesHtml = '<p>No high or medium risk packages were identified in this project.</p>';
  }

  html = html.replace(/{{#each risk_packages}}[\s\S]*?{{\/each}}/g, riskPackagesHtml);

  // Replace conclusion
  html = html.replace(/{{conclusion_intro}}/g, conclusion.intro);

  let conclusionPoints = '';
  conclusion.points.forEach(point => {
    conclusionPoints += `<li>${point}</li>`;
  });
  html = html.replace(/{{#each conclusion_points}}[\s\S]*?{{\/each}}/g, conclusionPoints);

  let recommendations = '';
  conclusion.recommendations.forEach(rec => {
    recommendations += `<li>${rec}</li>`;
  });
  html = html.replace(/{{#each recommendations}}[\s\S]*?{{\/each}}/g, recommendations);

  // Replace modules section
  html = html.replace(/{{modules_paragraph}}/g, 'A complete list of all modules and their licenses is available in the accompanying CSV file.');
  html = html.replace(/{{modules_note}}/g, 'The full list has been omitted from this report for brevity.');

  // Replace disclaimer
  html = html.replace(/{{disclaimer}}/g, 'This report is provided for informational purposes only and does not constitute legal advice. Consult with legal counsel for specific guidance on license compliance for your products.');

  return html;
}

// Main execution
try {
  // Generate CSV
  const csvData = generateCSV(licensesData);
  fs.writeFileSync('licenses-report.csv', csvData, 'utf8');
  console.log('CSV report generated: licenses-report.csv');

  // Generate HTML
  const htmlData = generateHTML();
  fs.writeFileSync('licenses-report.html', htmlData, 'utf8');
  console.log('HTML report generated: licenses-report.html');
} catch (error) {
  console.error('Error generating reports:', error);
}
