#!/bin/bash

set -euo pipefail  # Enable strict mode

BLUE_SERVICE="sales-hub-blue"
GREEN_SERVICE="sales-hub-green"

TIMEOUT=60  # Timeout in seconds
SLEEP_INTERVAL=5  # Time to sleep between retries in seconds
MAX_RETRIES=$((TIMEOUT / SLEEP_INTERVAL))

DOCKER_NETWORK="sales-hub"
TRAEFIK_API_URL="http://traefik:8080/api/http/services"

# Docker Compose command - use legacy syntax for production, modern syntax for other environments
if [ "${NODE_ENV:-}" = "production" ]; then
  DOCKER_COMPOSE_CMD="docker-compose"
else
  DOCKER_COMPOSE_CMD="docker compose"
fi

# Find which service is currently active
if docker ps --format "{{.Names}}" | grep -q "$BLUE_SERVICE"; then
  ACTIVE_SERVICE=$BLUE_SERVICE
  INACTIVE_SERVICE=$GREEN_SERVICE
elif docker ps --format "{{.Names}}" | grep -q "$GREEN_SERVICE"; then
  ACTIVE_SERVICE=$GREEN_SERVICE
  INACTIVE_SERVICE=$BLUE_SERVICE
else
  ACTIVE_SERVICE=""
  INACTIVE_SERVICE=$BLUE_SERVICE
fi

# Start the new environment
echo "Starting $INACTIVE_SERVICE container"
$DOCKER_COMPOSE_CMD -f $DOCKER_COMPOSE_FILE up --build --detach $INACTIVE_SERVICE

# Ensure Traefik is reachable by using a helper container
echo "Checking if Traefik is reachable within the Docker network..."
docker run --rm --network "$DOCKER_NETWORK" alpine/curl curl --fail --silent "$TRAEFIK_API_URL" > /dev/null || {
  echo "Unable to reach Traefik at $TRAEFIK_API_URL within the Docker network. Exiting."
  exit 1
}

# Check that Traefik recognizes the new container
echo "Checking if Traefik recognizes $INACTIVE_SERVICE..."
for ((i=1; i<=$MAX_RETRIES; i++)); do
  # N.B.: Because Traefik's port is mapped, we don't need to use the same trick as above for this to work on macOS.
  TRAEFIK_SERVER_STATUS=$(docker run --rm --network "$DOCKER_NETWORK" alpine/curl curl --fail --silent "$TRAEFIK_API_URL" | docker run -i --rm ghcr.io/jqlang/jq:1.7.1 -r --arg service "$INACTIVE_SERVICE" '.[] | select(.name | startswith($service)) | .serverStatus[]')
  
  if [ "$TRAEFIK_SERVER_STATUS" == "UP" ]; then
    echo "Traefik recognizes $INACTIVE_SERVICE as healthy"
    break
  fi

  sleep "$SLEEP_INTERVAL"
done

# If Traefik does not recognize the new container within the timeout, stop it and exit with an error
if [ "$TRAEFIK_SERVER_STATUS" != "UP" ]; then
  echo "Traefik did not recognize $INACTIVE_SERVICE within $TIMEOUT seconds"
  $DOCKER_COMPOSE_CMD -f $DOCKER_COMPOSE_FILE stop --timeout=30 $INACTIVE_SERVICE
  exit 1
fi

# Stop the old environment if it was previously running
if [[ -n "$ACTIVE_SERVICE" ]]; then
  echo "Stopping $ACTIVE_SERVICE container"
  $DOCKER_COMPOSE_CMD -f $DOCKER_COMPOSE_FILE stop --timeout=30 $ACTIVE_SERVICE
fi
