#!/bin/bash

# Resolve DOCKER_COMPOSE_FILE with fallback and error handling
DOCKER_COMPOSE_FILE=${1:-${DOCKER_COMPOSE_FILE}}

if [ -z "$DOCKER_COMPOSE_FILE" ]; then
  echo "Error: DOCKER_COMPOSE_FILE is not specified."
  echo "Usage: deploy-infra.sh <docker-compose-file>"
  echo "Or set the DOCKER_COMPOSE_FILE environment variable."
  exit 1
fi

# Docker Compose command - use legacy syntax for production, modern syntax for other environments
if [ "${NODE_ENV:-}" = "production" ]; then
  DOCKER_COMPOSE_CMD="docker-compose"
else
  DOCKER_COMPOSE_CMD="docker compose"
fi

echo "Starting docker infra services using" ${DOCKER_COMPOSE_FILE}

# Use the Dockerized jq to filter services with the 'infra=true' label and start them
$DOCKER_COMPOSE_CMD -f ${DOCKER_COMPOSE_FILE} config --format json | \
docker run --rm -i ghcr.io/jqlang/jq:1.7.1 -r '.services | to_entries[] | select(.value.labels["infra"] == "true") | .key' | \
xargs -I{} $DOCKER_COMPOSE_CMD -f ${DOCKER_COMPOSE_FILE} up -d {}

# Wait for 3 seconds after starting the services
echo "Waiting for services to stabilize..."
sleep 3
echo "Services started successfully."