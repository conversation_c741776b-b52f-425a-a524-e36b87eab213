<!DOCTYPE html>
<html>
<head>
    <!-- ===================== Template Meta ===================== -->
    <!-- This file is a Handlebars‑style template used by generative‑AI tooling
         to create repeatable Software‑License reports.  Replace {{ … }} tokens
         (or re‑map them for your preferred engine) with real values at render
         time.  Keep the overall structure and CSS so downstream consumers get a
         visually consistent document. -->

    <title>{{report_title}}</title>

    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; color: #333; }
        h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { color: #2c3e50; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        h3 { color: #3498db; }
        h4 { color: #2980b9; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; color: #2c3e50; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .high-risk { color: #e74c3c; font-weight: bold; }
        .medium-risk { color: #f39c12; font-weight: bold; }
        .low-risk { color: #27ae60; }
        .disclaimer { font-style: italic; border-top: 1px solid #ddd; padding-top: 20px; margin-top: 40px; color: #7f8c8d; }
        .recommendation { background-color: #eaf7fb; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0; }
        ul, ol { margin-bottom: 15px; }
        li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <!-- ===================== Header ===================== -->
    <h1>{{report_title}}</h1>
    <p>{{report_intro}}</p>

    <!-- ===================== Section 1: Distribution Table ===================== -->
    <h2>1. License Distribution Overview</h2>
    <table>
        <tr>
            <th>License Type</th>
            <th>Count</th>
            <th>Risk Level</th>
        </tr>
        {{#each license_distribution}}
        <tr>
            <td>{{type}}</td>
            <td>{{count}}</td>
            <td class="{{risk_class}}">{{risk_level}}</td>
        </tr>
        {{/each}}
    </table>

    <!-- ===================== Section 2: License Categories ===================== -->
    <h2>2. License Categories and Implications</h2>

    {{#each license_categories}}
        <h3>{{level}}-Risk Licenses</h3>
        <p>{{description}}</p>

        {{#each licenses}}
            <h4>{{name}}</h4>
            <ul>
                <li><strong>Permissions</strong>: {{permissions}}</li>
                <li><strong>Limitations</strong>: {{limitations}}</li>
                <li><strong>Conditions</strong>: {{conditions}}</li>
                <li><strong>Commercial Implications</strong>: {{implications}}</li>
            </ul>
        {{/each}}
    {{/each}}

    <!-- ===================== Section 3: Packages with Issues ===================== -->
    <h2>3. Packages with Potential Issues</h2>

    {{#each risk_packages}}
        <h3>{{risk_level}}-Risk Packages</h3>

        {{#each packages}}
            <h4>{{@index+1}}. {{name}} ({{license}})</h4>
            <ul>
                <li><strong>Version</strong>: {{version}}</li>
                <li><strong>Usage</strong>: {{usage}}</li>
                <li><strong>Risk</strong>: {{risk_level}}</li>
                <li><strong>Issue</strong>: {{issue}}</li>
                <li><strong>Recommendation</strong>: {{recommendation}}</li>
            </ul>
        {{/each}}
    {{/each}}

    <!-- ===================== Section 4: Conclusions ===================== -->
    <h2>4. Conclusion and Recommendations</h2>

    <p>{{conclusion_intro}}</p>
    <ol>
        {{#each conclusion_points}}
            <li>{{this}}</li>
        {{/each}}
    </ol>

    <div class="recommendation">
        <h3>Recommendations:</h3>
        <ol>
            {{#each recommendations}}
                <li>{{this}}</li>
            {{/each}}
        </ol>
    </div>

    <!-- ===================== Section 5: Modules List ===================== -->
    <h2>5. Complete List of Modules</h2>
    <p>{{modules_paragraph}}</p>
    <p><em>{{modules_note}}</em></p>

    <!-- ===================== Disclaimer ===================== -->
    <p class="disclaimer">{{disclaimer}}</p>
</body>
</html>
