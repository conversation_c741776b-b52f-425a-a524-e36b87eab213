Package Name,Version,License,Risk Level
"@acuminous/bitsyntax","0.1.2","MIT","Low"
"@ampproject/remapping","2.3.0","Apache-2.0","Low"
"@angular-devkit/core","17.1.2","MIT","Low"
"@angular-devkit/schematics-cli","17.1.2","MIT","Low"
"@angular-devkit/schematics","17.1.2","MIT","Low"
"@babel/code-frame","7.24.2","MIT","Low"
"@babel/compat-data","7.24.4","MIT","Low"
"@babel/core","7.24.5","MIT","Low"
"@babel/generator","7.24.5","MIT","Low"
"@babel/helper-compilation-targets","7.23.6","MIT","Low"
"@babel/helper-environment-visitor","7.22.20","MIT","Low"
"@babel/helper-function-name","7.23.0","MIT","Low"
"@babel/helper-hoist-variables","7.22.5","MIT","Low"
"@babel/helper-module-imports","7.24.3","MIT","Low"
"@babel/helper-module-transforms","7.24.5","MIT","Low"
"@babel/helper-plugin-utils","7.24.5","MIT","Low"
"@babel/helper-simple-access","7.24.5","MIT","Low"
"@babel/helper-split-export-declaration","7.24.5","MIT","Low"
"@babel/helper-string-parser","7.24.1","MIT","Low"
"@babel/helper-validator-identifier","7.24.5","MIT","Low"
"@babel/helper-validator-option","7.23.5","MIT","Low"
"@babel/helpers","7.24.5","MIT","Low"
"@babel/highlight","7.24.5","MIT","Low"
"@babel/parser","7.24.5","MIT","Low"
"@babel/plugin-syntax-async-generators","7.8.4","MIT","Low"
"@babel/plugin-syntax-bigint","7.8.3","MIT","Low"
"@babel/plugin-syntax-class-properties","7.12.13","MIT","Low"
"@babel/plugin-syntax-import-meta","7.10.4","MIT","Low"
"@babel/plugin-syntax-json-strings","7.8.3","MIT","Low"
"@babel/plugin-syntax-jsx","7.24.1","MIT","Low"
"@babel/plugin-syntax-logical-assignment-operators","7.10.4","MIT","Low"
"@babel/plugin-syntax-nullish-coalescing-operator","7.8.3","MIT","Low"
"@babel/plugin-syntax-numeric-separator","7.10.4","MIT","Low"
"@babel/plugin-syntax-object-rest-spread","7.8.3","MIT","Low"
"@babel/plugin-syntax-optional-catch-binding","7.8.3","MIT","Low"
"@babel/plugin-syntax-optional-chaining","7.8.3","MIT","Low"
"@babel/plugin-syntax-top-level-await","7.14.5","MIT","Low"
"@babel/plugin-syntax-typescript","7.24.1","MIT","Low"
"@babel/template","7.24.0","MIT","Low"
"@babel/traverse","7.24.5","MIT","Low"
"@babel/types","7.24.5","MIT","Low"
"@bcoe/v8-coverage","0.2.3","MIT","Low"
"@colors/colors","1.5.0","MIT","Low"
"@colors/colors","1.6.0","MIT","Low"
"@cspotcode/source-map-support","0.8.1","MIT","Low"
"@dabh/diagnostics","2.0.3","MIT","Low"
"@eslint-community/eslint-utils","4.4.0","MIT","Low"
"@eslint-community/regexpp","4.10.0","MIT","Low"
"@eslint/eslintrc","2.1.4","MIT","Low"
"@eslint/js","8.57.0","MIT","Low"
"@fig/complete-commander","3.2.0","MIT","Low"
"@golevelup/nestjs-discovery","4.0.1","MIT","Low"
"@graphql-tools/merge","9.0.0","MIT","Low"
"@graphql-tools/schema","10.0.0","MIT","Low"
"@graphql-tools/utils","10.0.8","MIT","Low"
"@graphql-typed-document-node/core","3.2.0","MIT","Low"
"@humanwhocodes/config-array","0.11.14","Apache-2.0","Low"
"@humanwhocodes/module-importer","1.0.1","Apache-2.0","Low"
"@humanwhocodes/object-schema","2.0.3","BSD-3-Clause","Low"
"@iarna/toml","2.2.5","ISC","Low"
"@isaacs/cliui","8.0.2","ISC","Low"
"@istanbuljs/load-nyc-config","1.1.0","ISC","Low"
"@istanbuljs/schema","0.1.3","MIT","Low"
"@jest/console","29.7.0","MIT","Low"
"@jest/core","29.7.0","MIT","Low"
"@jest/environment","29.7.0","MIT","Low"
"@jest/expect-utils","29.7.0","MIT","Low"
"@jest/expect","29.7.0","MIT","Low"
"@jest/fake-timers","29.7.0","MIT","Low"
"@jest/globals","29.7.0","MIT","Low"
"@jest/reporters","29.7.0","MIT","Low"
"@jest/schemas","29.6.3","MIT","Low"
"@jest/source-map","29.6.3","MIT","Low"
"@jest/test-result","29.7.0","MIT","Low"
"@jest/test-sequencer","29.7.0","MIT","Low"
"@jest/transform","29.7.0","MIT","Low"
"@jest/types","29.6.3","MIT","Low"
"@jridgewell/gen-mapping","0.3.5","MIT","Low"
"@jridgewell/resolve-uri","3.1.2","MIT","Low"
"@jridgewell/set-array","1.2.1","MIT","Low"
"@jridgewell/source-map","0.3.6","MIT","Low"
"@jridgewell/sourcemap-codec","1.4.15","MIT","Low"
"@jridgewell/trace-mapping","0.3.25","MIT","Low"
"@jridgewell/trace-mapping","0.3.9","MIT","Low"
"@ljharb/through","2.3.13","MIT","Low"
"@lukeed/csprng","1.1.0","MIT","Low"
"@microsoft/tsdoc","0.14.2","MIT","Low"
"@nestjs/axios","3.0.2","MIT","Low"
"@nestjs/cli","10.3.2","MIT","Low"
"@nestjs/common","10.3.8","MIT","Low"
"@nestjs/core","10.3.8","MIT","Low"
"@nestjs/cqrs","10.2.7","MIT","Low"
"@nestjs/graphql","12.0.11","MIT","Low"
"@nestjs/mapped-types","2.0.2","MIT","Low"
"@nestjs/mapped-types","2.0.5","MIT","Low"
"@nestjs/platform-express","10.3.8","MIT","Low"
"@nestjs/schedule","3.0.4","MIT","Low"
"@nestjs/schematics","10.1.1","MIT","Low"
"@nestjs/swagger","7.3.1","MIT","Low"
"@nestjs/terminus","10.2.3","MIT","Low"
"@nestjs/testing","10.3.8","MIT","Low"
"@nodelib/fs.scandir","2.1.5","MIT","Low"
"@nodelib/fs.stat","2.0.5","MIT","Low"
"@nodelib/fs.walk","1.2.8","MIT","Low"
"@ntegral/nestjs-sentry","4.0.1","ISC","Low"
"@nuxtjs/opencollective","0.3.2","MIT","Low"
"@octokit/auth-token","3.0.4","MIT","Low"
"@octokit/core","4.2.4","MIT","Low"
"@octokit/endpoint","7.0.6","MIT","Low"
"@octokit/graphql","5.0.6","MIT","Low"
"@octokit/openapi-types","18.1.1","MIT","Low"
"@octokit/plugin-paginate-rest","6.1.2","MIT","Low"
"@octokit/plugin-request-log","1.0.4","MIT","Low"
"@octokit/plugin-rest-endpoint-methods","7.2.3","MIT","Low"
"@octokit/request-error","3.0.3","MIT","Low"
"@octokit/request","6.2.8","MIT","Low"
"@octokit/rest","19.0.13","MIT","Low"
"@octokit/tsconfig","1.0.2","MIT","Low"
"@octokit/types","10.0.0","MIT","Low"
"@octokit/types","9.3.2","MIT","Low"
"@pkgjs/parseargs","0.11.0","MIT","Low"
"@pkgr/core","0.1.1","MIT","Low"
"@pnpm/config.env-replace","1.1.0","MIT","Low"
"@pnpm/network.ca-file","1.0.2","MIT","Low"
"@pnpm/npm-conf","2.3.1","MIT","Low"
"@prisma/client","5.13.0","Apache-2.0","Low"
"@prisma/debug","5.13.0","Apache-2.0","Low"
"@prisma/engines-version","5.13.0-23.b9a39a7ee606c28e3455d0fd60e78c3ba82b1a2b","Apache-2.0","Low"
"@prisma/engines","5.13.0","Apache-2.0","Low"
"@prisma/fetch-engine","5.13.0","Apache-2.0","Low"
"@prisma/get-platform","5.13.0","Apache-2.0","Low"
"@sentry-internal/tracing","7.114.0","MIT","Low"
"@sentry/core","7.114.0","MIT","Low"
"@sentry/core","7.120.3","MIT","Low"
"@sentry/hub","7.120.3","MIT","Low"
"@sentry/integrations","7.114.0","MIT","Low"
"@sentry/node","7.114.0","MIT","Low"
"@sentry/types","7.114.0","MIT","Low"
"@sentry/types","7.120.3","MIT","Low"
"@sentry/utils","7.114.0","MIT","Low"
"@sentry/utils","7.120.3","MIT","Low"
"@sinclair/typebox","0.27.8","MIT","Low"
"@sindresorhus/is","5.6.0","MIT","Low"
"@sinonjs/commons","3.0.1","BSD-3-Clause","Low"
"@sinonjs/fake-timers","10.3.0","BSD-3-Clause","Low"
"@suites/core.unit","3.0.1","Apache-2.0","Low"
"@suites/di.nestjs","3.0.1","Apache-2.0","Low"
"@suites/doubles.jest","3.0.1","Apache-2.0","Low"
"@suites/types.common","3.0.0","Apache-2.0","Low"
"@suites/types.di","3.0.0","Apache-2.0","Low"
"@suites/types.doubles","3.0.0","Apache-2.0","Low"
"@suites/unit","3.0.1","Apache-2.0","Low"
"@sw-web/nestjs-core","1.14.0","UNKNOWN","Unknown"
"@szmarczak/http-timer","5.0.1","MIT","Low"
"@tootallnate/quickjs-emscripten","0.23.0","MIT","Low"
"@tsconfig/node10","1.0.11","MIT","Low"
"@tsconfig/node12","1.0.11","MIT","Low"
"@tsconfig/node14","1.0.3","MIT","Low"
"@tsconfig/node16","1.0.4","MIT","Low"
"@types/amqplib","0.10.5","MIT","Low"
"@types/babel__core","7.20.5","MIT","Low"
"@types/babel__generator","7.6.8","MIT","Low"
"@types/babel__template","7.4.4","MIT","Low"
"@types/babel__traverse","7.20.5","MIT","Low"
"@types/body-parser","1.19.5","MIT","Low"
"@types/connect","3.4.38","MIT","Low"
"@types/cookie-parser","1.4.7","MIT","Low"
"@types/cookiejar","2.1.5","MIT","Low"
"@types/cron","2.4.0","MIT","Low"
"@types/deep-diff","1.0.5","MIT","Low"
"@types/eslint-scope","3.7.7","MIT","Low"
"@types/eslint","8.56.10","MIT","Low"
"@types/estree","1.0.7","MIT","Low"
"@types/express-serve-static-core","4.19.0","MIT","Low"
"@types/express","4.17.21","MIT","Low"
"@types/graceful-fs","4.1.9","MIT","Low"
"@types/http-cache-semantics","4.0.4","MIT","Low"
"@types/http-errors","2.0.4","MIT","Low"
"@types/inquirer","8.2.10","MIT","Low"
"@types/istanbul-lib-coverage","2.0.6","MIT","Low"
"@types/istanbul-lib-report","3.0.3","MIT","Low"
"@types/istanbul-reports","3.0.4","MIT","Low"
"@types/jest","29.5.12","MIT","Low"
"@types/json-schema","7.0.15","MIT","Low"
"@types/json5","0.0.29","MIT","Low"
"@types/lodash","4.17.1","MIT","Low"
"@types/luxon","3.3.8","MIT","Low"
"@types/methods","1.1.4","MIT","Low"
"@types/mime","1.3.5","MIT","Low"
"@types/node","20.12.12","MIT","Low"
"@types/pg","8.11.10","MIT","Low"
"@types/qs","6.9.15","MIT","Low"
"@types/range-parser","1.2.7","MIT","Low"
"@types/semver","7.5.8","MIT","Low"
"@types/send","0.17.4","MIT","Low"
"@types/serve-static","1.15.7","MIT","Low"
"@types/stack-utils","2.0.3","MIT","Low"
"@types/superagent","8.1.7","MIT","Low"
"@types/supertest","2.0.16","MIT","Low"
"@types/through","0.0.33","MIT","Low"
"@types/triple-beam","1.3.5","MIT","Low"
"@types/ua-parser-js","0.7.39","MIT","Low"
"@types/uuid","9.0.8","MIT","Low"
"@types/validator","13.11.10","MIT","Low"
"@types/yargs-parser","21.0.3","MIT","Low"
"@types/yargs","17.0.32","MIT","Low"
"@typescript-eslint/eslint-plugin","6.21.0","MIT","Low"
"@typescript-eslint/parser","6.21.0","BSD-2-Clause","Low"
"@typescript-eslint/scope-manager","5.62.0","MIT","Low"
"@typescript-eslint/scope-manager","6.21.0","MIT","Low"
"@typescript-eslint/type-utils","6.21.0","MIT","Low"
"@typescript-eslint/types","5.62.0","MIT","Low"
"@typescript-eslint/types","6.21.0","MIT","Low"
"@typescript-eslint/typescript-estree","5.62.0","BSD-2-Clause","Low"
"@typescript-eslint/typescript-estree","6.21.0","BSD-2-Clause","Low"
"@typescript-eslint/utils","5.62.0","MIT","Low"
"@typescript-eslint/utils","6.21.0","MIT","Low"
"@typescript-eslint/visitor-keys","5.62.0","MIT","Low"
"@typescript-eslint/visitor-keys","6.21.0","MIT","Low"
"@ungap/structured-clone","1.2.0","ISC","Low"
"@webassemblyjs/ast","1.14.1","MIT","Low"
"@webassemblyjs/floating-point-hex-parser","1.13.2","MIT","Low"
"@webassemblyjs/helper-api-error","1.13.2","MIT","Low"
"@webassemblyjs/helper-buffer","1.14.1","MIT","Low"
"@webassemblyjs/helper-numbers","1.13.2","MIT","Low"
"@webassemblyjs/helper-wasm-bytecode","1.13.2","MIT","Low"
"@webassemblyjs/helper-wasm-section","1.14.1","MIT","Low"
"@webassemblyjs/ieee754","1.13.2","MIT","Low"
"@webassemblyjs/leb128","1.13.2","Apache-2.0","Low"
"@webassemblyjs/utf8","1.13.2","MIT","Low"
"@webassemblyjs/wasm-edit","1.14.1","MIT","Low"
"@webassemblyjs/wasm-gen","1.14.1","MIT","Low"
"@webassemblyjs/wasm-opt","1.14.1","MIT","Low"
"@webassemblyjs/wasm-parser","1.14.1","MIT","Low"
"@webassemblyjs/wast-printer","1.14.1","MIT","Low"
"@xtuc/ieee754","1.2.0","BSD-3-Clause","Low"
"@xtuc/long","4.2.2","Apache-2.0","Low"
"accepts","1.3.8","MIT","Low"
"acorn-import-assertions","1.9.0","MIT","Low"
"acorn-jsx","5.3.2","MIT","Low"
"acorn-walk","8.3.2","MIT","Low"
"acorn","8.14.1","MIT","Low"
"agent-base","7.1.3","MIT","Low"
"ajv-formats","2.1.1","MIT","Low"
"ajv-keywords","3.5.2","MIT","Low"
"ajv-keywords","5.1.0","MIT","Low"
"ajv","6.12.6","MIT","Low"
"ajv","8.12.0","MIT","Low"
"amqp-connection-manager","4.1.14","MIT","Low"
"amqplib","0.10.4","MIT","Low"
"ansi-align","3.0.1","ISC","Low"
"ansi-colors","4.1.3","MIT","Low"
"ansi-escapes","4.3.2","MIT","Low"
"ansi-regex","5.0.1","MIT","Low"
"ansi-regex","6.0.1","MIT","Low"
"ansi-styles","3.2.1","MIT","Low"
"ansi-styles","4.3.0","MIT","Low"
"ansi-styles","5.2.0","MIT","Low"
"ansi-styles","6.2.1","MIT","Low"
"anymatch","3.1.3","ISC","Low"
"append-field","1.0.0","MIT","Low"
"arg","4.1.3","MIT","Low"
"argparse","1.0.10","MIT","Low"
"argparse","2.0.1","Python-2.0","Unknown"
"array-buffer-byte-length","1.0.2","MIT","Low"
"array-flatten","1.1.1","MIT","Low"
"array-includes","3.1.8","MIT","Low"
"array-timsort","1.0.3","MIT","Low"
"array-union","2.1.0","MIT","Low"
"array.prototype.findlastindex","1.2.5","MIT","Low"
"array.prototype.flat","1.3.2","MIT","Low"
"array.prototype.flatmap","1.3.2","MIT","Low"
"array.prototype.map","1.0.8","MIT","Low"
"arraybuffer.prototype.slice","1.0.4","MIT","Low"
"asap","2.0.6","MIT","Low"
"ast-types","0.13.4","MIT","Low"
"async-retry","1.3.3","MIT","Low"
"async","3.2.5","MIT","Low"
"asynckit","0.4.0","MIT","Low"
"available-typed-arrays","1.0.7","MIT","Low"
"axios","1.7.2","MIT","Low"
"babel-jest","29.7.0","MIT","Low"
"babel-plugin-istanbul","6.1.1","BSD-3-Clause","Low"
"babel-plugin-jest-hoist","29.6.3","MIT","Low"
"babel-preset-current-node-syntax","1.0.1","MIT","Low"
"babel-preset-jest","29.6.3","MIT","Low"
"backo2","1.0.2","MIT","Low"
"balanced-match","1.0.2","MIT","Low"
"base64-js","1.5.1","MIT","Low"
"basic-ftp","5.0.5","MIT","Low"
"before-after-hook","2.2.3","Apache-2.0","Low"
"big-integer","1.6.52","Unlicense","Low"
"binary-extensions","2.3.0","MIT","Low"
"bl","4.1.0","MIT","Low"
"bl","5.1.0","MIT","Low"
"body-parser","1.20.2","MIT","Low"
"body-parser","1.20.3","MIT","Low"
"boxen","5.1.2","MIT","Low"
"boxen","7.1.1","MIT","Low"
"bplist-parser","0.2.0","MIT","Low"
"brace-expansion","1.1.11","MIT","Low"
"brace-expansion","2.0.1","MIT","Low"
"braces","3.0.2","MIT","Low"
"browserslist","4.24.4","MIT","Low"
"bs-logger","0.2.6","MIT","Low"
"bser","2.1.1","Apache-2.0","Low"
"buffer-from","1.1.2","MIT","Low"
"buffer-more-ints","1.0.0","MIT","Low"
"buffer","5.7.1","MIT","Low"
"buffer","6.0.3","MIT","Low"
"bundle-name","3.0.0","MIT","Low"
"busboy","1.6.0","MIT","Low"
"bytes","3.1.2","MIT","Low"
"cacheable-lookup","7.0.0","MIT","Low"
"cacheable-request","10.2.14","MIT","Low"
"call-bind-apply-helpers","1.0.1","MIT","Low"
"call-bind","1.0.8","MIT","Low"
"call-bound","1.0.3","MIT","Low"
"callsites","3.1.0","MIT","Low"
"camelcase","5.3.1","MIT","Low"
"camelcase","6.3.0","MIT","Low"
"camelcase","7.0.1","MIT","Low"
"caniuse-lite","1.0.30001715","CC-BY-4.0","Unknown"
"chalk","2.4.2","MIT","Low"
"chalk","4.1.2","MIT","Low"
"chalk","5.3.0","MIT","Low"
"chalk","5.4.1","MIT","Low"
"char-regex","1.0.2","MIT","Low"
"chardet","0.7.0","MIT","Low"
"check-disk-space","3.4.0","MIT","Low"
"chokidar","3.5.3","MIT","Low"
"chokidar","3.6.0","MIT","Low"
"chrome-trace-event","1.0.3","MIT","Low"
"ci-info","3.9.0","MIT","Low"
"cjs-module-lexer","1.3.1","MIT","Low"
"class-transformer","0.5.1","MIT","Low"
"class-validator","0.14.1","MIT","Low"
"clean-architecture-nest","0.0.1","UNLICENSED","Unknown"
"cli-boxes","2.2.1","MIT","Low"
"cli-boxes","3.0.0","MIT","Low"
"cli-cursor","3.1.0","MIT","Low"
"cli-cursor","4.0.0","MIT","Low"
"cli-spinners","2.9.2","MIT","Low"
"cli-table3","0.6.3","MIT","Low"
"cli-width","3.0.0","ISC","Low"
"cli-width","4.1.0","ISC","Low"
"cliui","8.0.1","ISC","Low"
"clone","1.0.4","MIT","Low"
"co","4.6.0","MIT","Low"
"collect-v8-coverage","1.0.2","MIT","Low"
"color-convert","1.9.3","MIT","Low"
"color-convert","2.0.1","MIT","Low"
"color-name","1.1.3","MIT","Low"
"color-name","1.1.4","MIT","Low"
"color-string","1.9.1","MIT","Low"
"color","3.2.1","MIT","Low"
"colorspace","1.1.4","MIT","Low"
"combined-stream","1.0.8","MIT","Low"
"commander","11.1.0","MIT","Low"
"commander","2.20.3","MIT","Low"
"commander","4.1.1","MIT","Low"
"comment-json","4.2.3","MIT","Low"
"component-emitter","1.3.1","MIT","Low"
"concat-map","0.0.1","MIT","Low"
"concat-stream","1.6.2","MIT","Low"
"config-chain","1.1.13","MIT","Low"
"configstore","6.0.0","BSD-2-Clause","Low"
"confusing-browser-globals","1.0.11","MIT","Low"
"consola","2.15.3","MIT","Low"
"content-disposition","0.5.4","MIT","Low"
"content-type","1.0.5","MIT","Low"
"convert-source-map","2.0.0","MIT","Low"
"cookie-parser","1.4.6","MIT","Low"
"cookie-signature","1.0.6","MIT","Low"
"cookie","0.4.1","MIT","Low"
"cookie","0.6.0","MIT","Low"
"cookie","0.7.1","MIT","Low"
"cookiejar","2.1.4","MIT","Low"
"core-util-is","1.0.3","MIT","Low"
"cors","2.8.5","MIT","Low"
"cosmiconfig","8.3.6","MIT","Low"
"create-jest","29.7.0","MIT","Low"
"create-require","1.1.1","MIT","Low"
"cron","2.4.3","MIT","Low"
"cross-env","7.0.3","MIT","Low"
"cross-inspect","1.0.0","MIT","Low"
"cross-spawn","7.0.3","MIT","Low"
"crypto-random-string","4.0.0","MIT","Low"
"crypto","1.0.1","ISC","Low"
"data-uri-to-buffer","4.0.1","MIT","Low"
"data-uri-to-buffer","6.0.2","MIT","Low"
"data-view-buffer","1.0.2","MIT","Low"
"data-view-byte-length","1.0.2","MIT","Low"
"data-view-byte-offset","1.0.1","MIT","Low"
"dayjs","1.11.11","MIT","Low"
"debug","2.6.9","MIT","Low"
"debug","3.2.7","MIT","Low"
"debug","4.3.4","MIT","Low"
"decimal.js","10.4.3","MIT","Low"
"decompress-response","6.0.0","MIT","Low"
"dedent","1.5.3","MIT","Low"
"deep-diff","1.0.2","MIT","Low"
"deep-extend","0.6.0","MIT","Low"
"deep-is","0.1.4","MIT","Low"
"deepmerge","4.3.1","MIT","Low"
"default-browser-id","3.0.0","MIT","Low"
"default-browser","4.0.0","MIT","Low"
"defaults","1.0.4","MIT","Low"
"defer-to-connect","2.0.1","MIT","Low"
"define-data-property","1.1.4","MIT","Low"
"define-lazy-prop","3.0.0","MIT","Low"
"define-properties","1.2.1","MIT","Low"
"degenerator","5.0.1","MIT","Low"
"delayed-stream","1.0.0","MIT","Low"
"depd","2.0.0","MIT","Low"
"deprecation","2.3.1","ISC","Low"
"destroy","1.2.0","MIT","Low"
"detect-newline","3.1.0","MIT","Low"
"dezalgo","1.0.4","ISC","Low"
"diff-sequences","29.6.3","MIT","Low"
"diff","4.0.2","BSD-3-Clause","Low"
"dir-glob","3.0.1","MIT","Low"
"doctrine","2.1.0","Apache-2.0","Low"
"doctrine","3.0.0","Apache-2.0","Low"
"dot-prop","6.0.1","MIT","Low"
"dset","3.1.3","MIT","Low"
"dunder-proto","1.0.1","MIT","Low"
"eastasianwidth","0.2.0","MIT","Low"
"ee-first","1.1.1","MIT","Low"
"electron-to-chromium","1.5.139","ISC","Low"
"emittery","0.13.1","MIT","Low"
"emoji-regex","10.4.0","MIT","Low"
"emoji-regex","8.0.0","MIT","Low"
"emoji-regex","9.2.2","MIT","Low"
"enabled","2.0.0","MIT","Low"
"encodeurl","1.0.2","MIT","Low"
"encodeurl","2.0.0","MIT","Low"
"enhanced-resolve","5.18.1","MIT","Low"
"env-cmd","10.1.0","MIT","Low"
"env-var","7.4.2","MIT","Low"
"error-ex","1.3.2","MIT","Low"
"es-abstract","1.23.9","MIT","Low"
"es-array-method-boxes-properly","1.0.0","MIT","Low"
"es-define-property","1.0.1","MIT","Low"
"es-errors","1.3.0","MIT","Low"
"es-get-iterator","1.1.3","MIT","Low"
"es-module-lexer","1.5.2","MIT","Low"
"es-object-atoms","1.0.0","MIT","Low"
"es-set-tostringtag","2.1.0","MIT","Low"
"es-shim-unscopables","1.0.2","MIT","Low"
"es-to-primitive","1.3.0","MIT","Low"
"escalade","3.2.0","MIT","Low"
"escape-goat","4.0.0","MIT","Low"
"escape-html","1.0.3","MIT","Low"
"escape-string-regexp","1.0.5","MIT","Low"
"escape-string-regexp","2.0.0","MIT","Low"
"escape-string-regexp","4.0.0","MIT","Low"
"escape-string-regexp","5.0.0","MIT","Low"
"escodegen","2.1.0","BSD-2-Clause","Low"
"eslint-config-airbnb-base","15.0.0","MIT","Low"
"eslint-config-airbnb-typescript","17.1.0","MIT","Low"
"eslint-config-prettier","9.1.0","MIT","Low"
"eslint-import-resolver-node","0.3.9","MIT","Low"
"eslint-module-utils","2.8.1","MIT","Low"
"eslint-plugin-import","2.29.1","MIT","Low"
"eslint-plugin-jest","27.9.0","MIT","Low"
"eslint-plugin-prettier","5.1.3","MIT","Low"
"eslint-scope","5.1.1","BSD-2-Clause","Low"
"eslint-scope","7.2.2","BSD-2-Clause","Low"
"eslint-visitor-keys","3.4.3","Apache-2.0","Low"
"eslint","8.57.0","MIT","Low"
"espree","9.6.1","BSD-2-Clause","Low"
"esprima","4.0.1","BSD-2-Clause","Low"
"esquery","1.5.0","BSD-3-Clause","Low"
"esrecurse","4.3.0","BSD-2-Clause","Low"
"estraverse","4.3.0","BSD-2-Clause","Low"
"estraverse","5.3.0","BSD-2-Clause","Low"
"esutils","2.0.3","BSD-2-Clause","Low"
"etag","1.8.1","MIT","Low"
"eventemitter2","6.4.9","MIT","Low"
"eventemitter3","3.1.2","MIT","Low"
"events","3.3.0","MIT","Low"
"execa","5.1.1","MIT","Low"
"execa","7.2.0","MIT","Low"
"exit","0.1.2","MIT","Low"
"expect","29.7.0","MIT","Low"
"express","4.19.2","MIT","Low"
"express","4.21.2","MIT","Low"
"external-editor","3.1.0","MIT","Low"
"fast-deep-equal","3.1.3","MIT","Low"
"fast-diff","1.3.0","Apache-2.0","Low"
"fast-glob","3.3.2","MIT","Low"
"fast-json-stable-stringify","2.1.0","MIT","Low"
"fast-levenshtein","2.0.6","MIT","Low"
"fast-safe-stringify","2.1.1","MIT","Low"
"fastq","1.17.1","ISC","Low"
"fb-watchman","2.0.2","Apache-2.0","Low"
"fecha","4.2.3","MIT","Low"
"fetch-blob","3.2.0","MIT","Low"
"figures","3.2.0","MIT","Low"
"figures","5.0.0","MIT","Low"
"file-entry-cache","6.0.1","MIT","Low"
"fill-range","7.0.1","MIT","Low"
"finalhandler","1.2.0","MIT","Low"
"finalhandler","1.3.1","MIT","Low"
"find-up","4.1.0","MIT","Low"
"find-up","5.0.0","MIT","Low"
"flat-cache","3.2.0","MIT","Low"
"flatted","3.3.1","ISC","Low"
"fn.name","1.1.0","MIT","Low"
"follow-redirects","1.15.6","MIT","Low"
"for-each","0.3.3","MIT","Low"
"foreground-child","3.1.1","ISC","Low"
"fork-ts-checker-webpack-plugin","9.0.2","MIT","Low"
"form-data-encoder","2.1.4","MIT","Low"
"form-data","4.0.0","MIT","Low"
"formdata-polyfill","4.0.10","MIT","Low"
"formidable","2.1.2","MIT","Low"
"forwarded","0.2.0","MIT","Low"
"fresh","0.5.2","MIT","Low"
"fs-extra","10.1.0","MIT","Low"
"fs-monkey","1.0.6","Unlicense","Low"
"fs.realpath","1.0.0","ISC","Low"
"function-bind","1.1.2","MIT","Low"
"function.prototype.name","1.1.8","MIT","Low"
"functions-have-names","1.2.3","MIT","Low"
"gensync","1.0.0-beta.2","MIT","Low"
"get-caller-file","2.0.5","ISC","Low"
"get-intrinsic","1.2.7","MIT","Low"
"get-package-type","0.1.0","MIT","Low"
"get-proto","1.0.1","MIT","Low"
"get-stream","6.0.1","MIT","Low"
"get-symbol-description","1.1.0","MIT","Low"
"get-uri","6.0.4","MIT","Low"
"git-up","7.0.0","MIT","Low"
"git-url-parse","13.1.0","MIT","Low"
"glob-parent","5.1.2","ISC","Low"
"glob-parent","6.0.2","ISC","Low"
"glob-to-regexp","0.4.1","BSD-2-Clause","Low"
"glob","10.3.10","ISC","Low"
"glob","7.2.3","ISC","Low"
"glob","9.3.5","ISC","Low"
"global-dirs","3.0.1","MIT","Low"
"globals","11.12.0","MIT","Low"
"globals","13.24.0","MIT","Low"
"globalthis","1.0.4","MIT","Low"
"globby","11.1.0","MIT","Low"
"globby","13.2.2","MIT","Low"
"gopd","1.2.0","MIT","Low"
"got","12.6.1","MIT","Low"
"got","13.0.0","MIT","Low"
"graceful-fs","4.2.10","ISC","Low"
"graceful-fs","4.2.11","ISC","Low"
"graphemer","1.4.0","MIT","Low"
"graphql-tag","2.12.6","MIT","Low"
"graphql-ws","5.14.2","MIT","Low"
"graphql","16.10.0","MIT","Low"
"has-bigints","1.1.0","MIT","Low"
"has-flag","3.0.0","MIT","Low"
"has-flag","4.0.0","MIT","Low"
"has-own-prop","2.0.0","MIT","Low"
"has-property-descriptors","1.0.2","MIT","Low"
"has-proto","1.2.0","MIT","Low"
"has-symbols","1.1.0","MIT","Low"
"has-tostringtag","1.0.2","MIT","Low"
"has-yarn","3.0.0","MIT","Low"
"hasown","2.0.2","MIT","Low"
"hexoid","1.0.0","MIT","Low"
"html-escaper","2.0.2","MIT","Low"
"http-cache-semantics","4.1.1","BSD-2-Clause","Low"
"http-errors","2.0.0","MIT","Low"
"http-proxy-agent","7.0.2","MIT","Low"
"http2-wrapper","2.2.1","MIT","Low"
"https-proxy-agent","7.0.6","MIT","Low"
"human-signals","2.1.0","Apache-2.0","Low"
"human-signals","4.3.1","Apache-2.0","Low"
"husky","8.0.3","MIT","Low"
"iconv-lite","0.4.24","MIT","Low"
"ieee754","1.2.1","BSD-3-Clause","Low"
"ignore","5.3.1","MIT","Low"
"immediate","3.0.6","MIT","Low"
"import-fresh","3.3.0","MIT","Low"
"import-lazy","4.0.0","MIT","Low"
"import-local","3.1.0","MIT","Low"
"imurmurhash","0.1.4","MIT","Low"
"inflight","1.0.6","ISC","Low"
"inherits","2.0.4","ISC","Low"
"ini","1.3.8","ISC","Low"
"ini","2.0.0","ISC","Low"
"inquirer","8.2.6","MIT","Low"
"inquirer","9.2.11","MIT","Low"
"inquirer","9.2.12","MIT","Low"
"internal-slot","1.1.0","MIT","Low"
"interpret","1.4.0","MIT","Low"
"ip-address","9.0.5","MIT","Low"
"ipaddr.js","1.9.1","MIT","Low"
"is-arguments","1.2.0","MIT","Low"
"is-array-buffer","3.0.5","MIT","Low"
"is-arrayish","0.2.1","MIT","Low"
"is-arrayish","0.3.2","MIT","Low"
"is-async-function","2.1.0","MIT","Low"
"is-bigint","1.1.0","MIT","Low"
"is-binary-path","2.1.0","MIT","Low"
"is-boolean-object","1.2.1","MIT","Low"
"is-callable","1.2.7","MIT","Low"
"is-ci","3.0.1","MIT","Low"
"is-core-module","2.13.1","MIT","Low"
"is-data-view","1.0.2","MIT","Low"
"is-date-object","1.1.0","MIT","Low"
"is-docker","2.2.1","MIT","Low"
"is-docker","3.0.0","MIT","Low"
"is-extglob","2.1.1","MIT","Low"
"is-finalizationregistry","1.1.1","MIT","Low"
"is-fullwidth-code-point","3.0.0","MIT","Low"
"is-generator-fn","2.1.0","MIT","Low"
"is-generator-function","1.1.0","MIT","Low"
"is-glob","4.0.3","MIT","Low"
"is-inside-container","1.0.0","MIT","Low"
"is-installed-globally","0.4.0","MIT","Low"
"is-interactive","1.0.0","MIT","Low"
"is-interactive","2.0.0","MIT","Low"
"is-map","2.0.3","MIT","Low"
"is-npm","6.0.0","MIT","Low"
"is-number-object","1.1.1","MIT","Low"
"is-number","7.0.0","MIT","Low"
"is-obj","2.0.0","MIT","Low"
"is-path-inside","3.0.3","MIT","Low"
"is-plain-object","5.0.0","MIT","Low"
"is-regex","1.2.1","MIT","Low"
"is-set","2.0.3","MIT","Low"
"is-shared-array-buffer","1.0.4","MIT","Low"
"is-ssh","1.4.0","MIT","Low"
"is-stream","2.0.1","MIT","Low"
"is-stream","3.0.0","MIT","Low"
"is-string","1.1.1","MIT","Low"
"is-symbol","1.1.1","MIT","Low"
"is-typed-array","1.1.15","MIT","Low"
"is-typedarray","1.0.0","MIT","Low"
"is-unicode-supported","0.1.0","MIT","Low"
"is-unicode-supported","1.3.0","MIT","Low"
"is-weakmap","2.0.2","MIT","Low"
"is-weakref","1.1.0","MIT","Low"
"is-weakset","2.0.4","MIT","Low"
"is-wsl","2.2.0","MIT","Low"
"is-yarn-global","0.4.1","MIT","Low"
"isarray","0.0.1","MIT","Low"
"isarray","1.0.0","MIT","Low"
"isarray","2.0.5","MIT","Low"
"isexe","2.0.0","ISC","Low"
"issue-parser","6.0.0","MIT","Low"
"istanbul-lib-coverage","3.2.2","BSD-3-Clause","Low"
"istanbul-lib-instrument","5.2.1","BSD-3-Clause","Low"
"istanbul-lib-instrument","6.0.2","BSD-3-Clause","Low"
"istanbul-lib-report","3.0.1","BSD-3-Clause","Low"
"istanbul-lib-source-maps","4.0.1","BSD-3-Clause","Low"
"istanbul-reports","3.1.7","BSD-3-Clause","Low"
"iterall","1.3.0","MIT","Low"
"iterare","1.2.1","ISC","Low"
"iterate-iterator","1.0.2","MIT","Low"
"iterate-value","1.0.2","MIT","Low"
"jackspeak","2.3.6","BlueOak-1.0.0","Unknown"
"jest-changed-files","29.7.0","MIT","Low"
"jest-circus","29.7.0","MIT","Low"
"jest-cli","29.7.0","MIT","Low"
"jest-config","29.7.0","MIT","Low"
"jest-diff","29.7.0","MIT","Low"
"jest-docblock","29.7.0","MIT","Low"
"jest-each","29.7.0","MIT","Low"
"jest-environment-node","29.7.0","MIT","Low"
"jest-get-type","29.6.3","MIT","Low"
"jest-haste-map","29.7.0","MIT","Low"
"jest-leak-detector","29.7.0","MIT","Low"
"jest-matcher-utils","29.7.0","MIT","Low"
"jest-message-util","29.7.0","MIT","Low"
"jest-mock","29.7.0","MIT","Low"
"jest-pnp-resolver","1.2.3","MIT","Low"
"jest-regex-util","29.6.3","MIT","Low"
"jest-resolve-dependencies","29.7.0","MIT","Low"
"jest-resolve","29.7.0","MIT","Low"
"jest-runner","29.7.0","MIT","Low"
"jest-runtime","29.7.0","MIT","Low"
"jest-snapshot","29.7.0","MIT","Low"
"jest-util","29.7.0","MIT","Low"
"jest-validate","29.7.0","MIT","Low"
"jest-watcher","29.7.0","MIT","Low"
"jest-worker","27.5.1","MIT","Low"
"jest-worker","29.7.0","MIT","Low"
"jest","29.7.0","MIT","Low"
"js-tokens","4.0.0","MIT","Low"
"js-yaml","3.14.1","MIT","Low"
"js-yaml","4.1.0","MIT","Low"
"jsbn","1.1.0","MIT","Low"
"jsesc","2.5.2","MIT","Low"
"json-buffer","3.0.1","MIT","Low"
"json-parse-even-better-errors","2.3.1","MIT","Low"
"json-schema-traverse","0.4.1","MIT","Low"
"json-schema-traverse","1.0.0","MIT","Low"
"json-stable-stringify-without-jsonify","1.0.1","MIT","Low"
"json5","1.0.2","MIT","Low"
"json5","2.2.3","MIT","Low"
"jsonc-parser","3.2.0","MIT","Low"
"jsonc-parser","3.2.1","MIT","Low"
"jsonfile","6.1.0","MIT","Low"
"keyv","4.5.4","MIT","Low"
"kleur","3.0.3","MIT","Low"
"kuler","2.0.0","MIT","Low"
"latest-version","7.0.0","MIT","Low"
"leven","3.1.0","MIT","Low"
"levn","0.4.1","MIT","Low"
"libphonenumber-js","1.11.1","MIT","Low"
"lie","3.1.1","MIT","Low"
"lines-and-columns","1.2.4","MIT","Low"
"loader-runner","4.3.0","MIT","Low"
"localforage","1.10.0","Apache-2.0","Low"
"locate-path","5.0.0","MIT","Low"
"locate-path","6.0.0","MIT","Low"
"lodash.capitalize","4.2.1","MIT","Low"
"lodash.escaperegexp","4.1.2","MIT","Low"
"lodash.isequal","4.5.0","MIT","Low"
"lodash.isplainobject","4.0.6","MIT","Low"
"lodash.isstring","4.0.1","MIT","Low"
"lodash.memoize","4.1.2","MIT","Low"
"lodash.merge","4.6.2","MIT","Low"
"lodash.uniqby","4.7.0","MIT","Low"
"lodash","4.17.21","MIT","Low"
"log-symbols","4.1.0","MIT","Low"
"log-symbols","5.1.0","MIT","Low"
"logform","2.6.0","MIT","Low"
"lowercase-keys","3.0.0","MIT","Low"
"lru-cache","10.2.2","ISC","Low"
"lru-cache","5.1.1","ISC","Low"
"lru-cache","6.0.0","ISC","Low"
"lru-cache","7.18.3","ISC","Low"
"luxon","3.3.0","MIT","Low"
"macos-release","3.3.0","MIT","Low"
"magic-string","0.30.5","MIT","Low"
"make-dir","4.0.0","MIT","Low"
"make-error","1.3.6","ISC","Low"
"makeerror","1.0.12","BSD-3-Clause","Low"
"math-intrinsics","1.1.0","MIT","Low"
"media-typer","0.3.0","MIT","Low"
"memfs","3.6.0","Unlicense","Low"
"merge-descriptors","1.0.1","MIT","Low"
"merge-descriptors","1.0.3","MIT","Low"
"merge-stream","2.0.0","MIT","Low"
"merge2","1.4.1","MIT","Low"
"methods","1.1.2","MIT","Low"
"micromatch","4.0.5","MIT","Low"
"mime-db","1.52.0","MIT","Low"
"mime-types","2.1.35","MIT","Low"
"mime","1.6.0","MIT","Low"
"mime","2.6.0","MIT","Low"
"mimic-fn","2.1.0","MIT","Low"
"mimic-fn","4.0.0","MIT","Low"
"mimic-response","3.1.0","MIT","Low"
"mimic-response","4.0.0","MIT","Low"
"minimatch","3.1.2","ISC","Low"
"minimatch","8.0.4","ISC","Low"
"minimatch","9.0.3","ISC","Low"
"minimist","1.2.8","MIT","Low"
"minipass","4.2.8","ISC","Low"
"minipass","7.1.1","ISC","Low"
"mkdirp","0.5.6","MIT","Low"
"ms","2.0.0","MIT","Low"
"ms","2.1.2","MIT","Low"
"ms","2.1.3","MIT","Low"
"multer","1.4.4-lts.1","MIT","Low"
"mute-stream","0.0.8","ISC","Low"
"mute-stream","1.0.0","ISC","Low"
"natural-compare","1.4.0","MIT","Low"
"negotiator","0.6.3","MIT","Low"
"neo-async","2.6.2","MIT","Low"
"nest-commander","3.15.0","MIT","Low"
"netmask","2.0.2","MIT","Low"
"new-github-release-url","2.0.0","MIT","Low"
"node-abort-controller","3.1.1","MIT","Low"
"node-domexception","1.0.0","MIT","Low"
"node-emoji","1.11.0","MIT","Low"
"node-fetch","2.7.0","MIT","Low"
"node-fetch","3.3.2","MIT","Low"
"node-int64","0.4.0","MIT","Low"
"node-releases","2.0.19","MIT","Low"
"normalize-path","3.0.0","MIT","Low"
"normalize-url","8.0.1","MIT","Low"
"npm-run-path","4.0.1","MIT","Low"
"npm-run-path","5.3.0","MIT","Low"
"object-assign","4.1.1","MIT","Low"
"object-inspect","1.13.3","MIT","Low"
"object-keys","1.1.1","MIT","Low"
"object.assign","4.1.7","MIT","Low"
"object.entries","1.1.8","MIT","Low"
"object.fromentries","2.0.8","MIT","Low"
"object.groupby","1.0.3","MIT","Low"
"object.values","1.2.0","MIT","Low"
"obuf","1.1.2","MIT","Low"
"on-finished","2.4.1","MIT","Low"
"once","1.4.0","ISC","Low"
"one-time","1.0.0","MIT","Low"
"onetime","5.1.2","MIT","Low"
"onetime","6.0.0","MIT","Low"
"open","9.1.0","MIT","Low"
"optionator","0.9.4","MIT","Low"
"ora","5.4.1","MIT","Low"
"ora","7.0.1","MIT","Low"
"os-name","5.1.0","MIT","Low"
"os-tmpdir","1.0.2","MIT","Low"
"own-keys","1.0.1","MIT","Low"
"p-cancelable","3.0.0","MIT","Low"
"p-limit","2.3.0","MIT","Low"
"p-limit","3.1.0","MIT","Low"
"p-locate","4.1.0","MIT","Low"
"p-locate","5.0.0","MIT","Low"
"p-try","2.2.0","MIT","Low"
"pac-proxy-agent","7.1.0","MIT","Low"
"pac-resolver","7.0.1","MIT","Low"
"package-json","8.1.1","MIT","Low"
"parent-module","1.0.1","MIT","Low"
"parse-json","5.2.0","MIT","Low"
"parse-path","7.0.0","MIT","Low"
"parse-url","8.1.0","MIT","Low"
"parseurl","1.3.3","MIT","Low"
"path-exists","4.0.0","MIT","Low"
"path-is-absolute","1.0.1","MIT","Low"
"path-key","3.1.1","MIT","Low"
"path-key","4.0.0","MIT","Low"
"path-parse","1.0.7","MIT","Low"
"path-scurry","1.11.1","BlueOak-1.0.0","Unknown"
"path-to-regexp","0.1.12","MIT","Low"
"path-to-regexp","0.1.7","MIT","Low"
"path-to-regexp","3.2.0","MIT","Low"
"path-type","4.0.0","MIT","Low"
"pg-cloudflare","1.1.1","MIT","Low"
"pg-connection-string","2.7.0","MIT","Low"
"pg-int8","1.0.1","ISC","Low"
"pg-numeric","1.0.2","ISC","Low"
"pg-pool","3.7.0","MIT","Low"
"pg-protocol","1.7.0","MIT","Low"
"pg-types","2.2.0","MIT","Low"
"pg-types","4.0.2","MIT","Low"
"pg","8.13.1","MIT","Low"
"pgpass","1.0.5","MIT","Low"
"picocolors","1.1.1","ISC","Low"
"picomatch","2.3.1","MIT","Low"
"picomatch","3.0.1","MIT","Low"
"pirates","4.0.6","MIT","Low"
"pkg-dir","4.2.0","MIT","Low"
"pluralize","8.0.0","MIT","Low"
"possible-typed-array-names","1.0.0","MIT","Low"
"postgres-array","2.0.0","MIT","Low"
"postgres-array","3.0.2","MIT","Low"
"postgres-bytea","1.0.0","MIT","Low"
"postgres-bytea","3.0.0","MIT","Low"
"postgres-date","1.0.7","MIT","Low"
"postgres-date","2.1.0","MIT","Low"
"postgres-interval","1.2.0","MIT","Low"
"postgres-interval","3.0.0","MIT","Low"
"postgres-range","1.1.4","MIT","Low"
"prelude-ls","1.2.1","MIT","Low"
"prettier-linter-helpers","1.0.0","MIT","Low"
"prettier","3.2.5","MIT","Low"
"pretty-format","29.7.0","MIT","Low"
"prisma","5.13.0","Apache-2.0","Low"
"process-nextick-args","2.0.1","MIT","Low"
"promise-breaker","6.0.0","MIT","Low"
"promise.allsettled","1.0.7","MIT","Low"
"prompts","2.4.2","MIT","Low"
"proto-list","1.2.4","ISC","Low"
"protocols","2.0.1","MIT","Low"
"proxy-addr","2.0.7","MIT","Low"
"proxy-agent","6.3.1","MIT","Low"
"proxy-from-env","1.1.0","MIT","Low"
"punycode","2.3.1","MIT","Low"
"pupa","3.1.0","MIT","Low"
"pure-rand","6.1.0","MIT","Low"
"qs","6.11.0","BSD-3-Clause","Low"
"qs","6.13.0","BSD-3-Clause","Low"
"querystringify","2.2.0","MIT","Low"
"queue-microtask","1.2.3","MIT","Low"
"quick-lru","5.1.1","MIT","Low"
"randombytes","2.1.0","MIT","Low"
"range-parser","1.2.1","MIT","Low"
"raw-body","2.5.2","MIT","Low"
"rc","1.2.8","(BSD-2-Clause OR MIT OR Apache-2.0)","Low"
"react-is","18.3.1","MIT","Low"
"readable-stream","1.1.14","MIT","Low"
"readable-stream","2.3.8","MIT","Low"
"readable-stream","3.6.2","MIT","Low"
"readdirp","3.6.0","MIT","Low"
"rechoir","0.6.2","MIT","Low"
"reflect-metadata","0.1.14","Apache-2.0","Low"
"reflect.getprototypeof","1.0.10","MIT","Low"
"regexp.prototype.flags","1.5.4","MIT","Low"
"registry-auth-token","5.0.3","MIT","Low"
"registry-url","6.0.1","MIT","Low"
"release-it","16.3.0","MIT","Low"
"repeat-string","1.6.1","MIT","Low"
"require-directory","2.1.1","MIT","Low"
"require-from-string","2.0.2","MIT","Low"
"requires-port","1.0.0","MIT","Low"
"resolve-alpn","1.2.1","MIT","Low"
"resolve-cwd","3.0.0","MIT","Low"
"resolve-from","4.0.0","MIT","Low"
"resolve-from","5.0.0","MIT","Low"
"resolve.exports","2.0.2","MIT","Low"
"resolve","1.22.8","MIT","Low"
"responselike","3.0.0","MIT","Low"
"restore-cursor","3.1.0","MIT","Low"
"restore-cursor","4.0.0","MIT","Low"
"retry","0.13.1","MIT","Low"
"reusify","1.0.4","MIT","Low"
"rimraf","3.0.2","ISC","Low"
"rimraf","4.4.1","ISC","Low"
"run-applescript","5.0.0","MIT","Low"
"run-async","2.4.1","MIT","Low"
"run-async","3.0.0","MIT","Low"
"run-parallel","1.2.0","MIT","Low"
"rxjs","7.8.1","Apache-2.0","Low"
"safe-array-concat","1.1.3","MIT","Low"
"safe-buffer","5.1.2","MIT","Low"
"safe-buffer","5.2.1","MIT","Low"
"safe-push-apply","1.0.0","MIT","Low"
"safe-regex-test","1.1.0","MIT","Low"
"safe-stable-stringify","2.4.3","MIT","Low"
"safer-buffer","2.1.2","MIT","Low"
"schema-utils","3.3.0","MIT","Low"
"schema-utils","4.3.0","MIT","Low"
"semver-diff","4.0.0","MIT","Low"
"semver","6.3.1","ISC","Low"
"semver","7.5.4","ISC","Low"
"semver","7.6.2","ISC","Low"
"send","0.18.0","MIT","Low"
"send","0.19.0","MIT","Low"
"serialize-javascript","6.0.2","BSD-3-Clause","Low"
"serve-static","1.15.0","MIT","Low"
"serve-static","1.16.2","MIT","Low"
"set-function-length","1.2.2","MIT","Low"
"set-function-name","2.0.2","MIT","Low"
"set-proto","1.0.0","MIT","Low"
"setprototypeof","1.2.0","ISC","Low"
"shebang-command","2.0.0","MIT","Low"
"shebang-regex","3.0.0","MIT","Low"
"shelljs","0.8.5","BSD-3-Clause","Low"
"side-channel-list","1.0.0","MIT","Low"
"side-channel-map","1.0.1","MIT","Low"
"side-channel-weakmap","1.0.2","MIT","Low"
"side-channel","1.1.0","MIT","Low"
"signal-exit","3.0.7","ISC","Low"
"signal-exit","4.1.0","ISC","Low"
"simple-swizzle","0.2.2","MIT","Low"
"sisteransi","1.0.5","MIT","Low"
"slash","3.0.0","MIT","Low"
"slash","4.0.0","MIT","Low"
"smart-buffer","4.2.0","MIT","Low"
"socks-proxy-agent","8.0.5","MIT","Low"
"socks","2.8.3","MIT","Low"
"source-map-support","0.5.13","MIT","Low"
"source-map-support","0.5.21","MIT","Low"
"source-map","0.6.1","BSD-3-Clause","Low"
"source-map","0.7.4","BSD-3-Clause","Low"
"split2","4.2.0","ISC","Low"
"sprintf-js","1.0.3","BSD-3-Clause","Low"
"sprintf-js","1.1.3","BSD-3-Clause","Low"
"stack-trace","0.0.10","MIT","Low"
"stack-utils","2.0.6","MIT","Low"
"statuses","2.0.1","MIT","Low"
"stdin-discarder","0.1.0","MIT","Low"
"stop-iteration-iterator","1.1.0","MIT","Low"
"streamsearch","1.1.0","MIT","Low"
"string-length","4.0.2","MIT","Low"
"string-width","4.2.3","MIT","Low"
"string-width","5.1.2","MIT","Low"
"string-width","6.1.0","MIT","Low"
"string.prototype.trim","1.2.10","MIT","Low"
"string.prototype.trimend","1.0.9","MIT","Low"
"string.prototype.trimstart","1.0.8","MIT","Low"
"string_decoder","0.10.31","MIT","Low"
"string_decoder","1.1.1","MIT","Low"
"string_decoder","1.3.0","MIT","Low"
"strip-ansi","6.0.1","MIT","Low"
"strip-ansi","7.1.0","MIT","Low"
"strip-bom","3.0.0","MIT","Low"
"strip-bom","4.0.0","MIT","Low"
"strip-final-newline","2.0.0","MIT","Low"
"strip-final-newline","3.0.0","MIT","Low"
"strip-json-comments","2.0.1","MIT","Low"
"strip-json-comments","3.1.1","MIT","Low"
"subscriptions-transport-ws","0.11.0","MIT","Low"
"superagent","8.1.2","MIT","Low"
"supertest","6.3.4","MIT","Low"
"supports-color","5.5.0","MIT","Low"
"supports-color","7.2.0","MIT","Low"
"supports-color","8.1.1","MIT","Low"
"supports-preserve-symlinks-flag","1.0.0","MIT","Low"
"swagger-ui-dist","5.11.2","Apache-2.0","Low"
"symbol-observable","1.2.0","MIT","Low"
"symbol-observable","4.0.0","MIT","Low"
"synckit","0.8.8","MIT","Low"
"tapable","2.2.1","MIT","Low"
"terser-webpack-plugin","5.3.14","MIT","Low"
"terser","5.39.0","BSD-2-Clause","Low"
"test-exclude","6.0.0","ISC","Low"
"text-hex","1.0.0","MIT","Low"
"text-table","0.2.0","MIT","Low"
"through","2.3.8","MIT","Low"
"titleize","3.0.0","MIT","Low"
"tmp","0.0.33","MIT","Low"
"tmpl","1.0.5","BSD-3-Clause","Low"
"to-fast-properties","2.0.0","MIT","Low"
"to-regex-range","5.0.1","MIT","Low"
"toidentifier","1.0.1","MIT","Low"
"tr46","0.0.3","MIT","Low"
"tree-kill","1.2.2","MIT","Low"
"triple-beam","1.4.1","MIT","Low"
"ts-api-utils","1.3.0","MIT","Low"
"ts-jest","29.1.2","MIT","Low"
"ts-loader","9.5.1","MIT","Low"
"ts-node","10.9.2","MIT","Low"
"tsconfig-paths-webpack-plugin","4.1.0","MIT","Low"
"tsconfig-paths","3.15.0","MIT","Low"
"tsconfig-paths","4.2.0","MIT","Low"
"tslib","1.14.1","0BSD","Low"
"tslib","2.6.2","0BSD","Low"
"tsutils","3.21.0","MIT","Low"
"type-check","0.4.0","MIT","Low"
"type-detect","4.0.8","MIT","Low"
"type-fest","0.20.2","(MIT OR CC0-1.0)","Low"
"type-fest","0.21.3","(MIT OR CC0-1.0)","Low"
"type-fest","1.4.0","(MIT OR CC0-1.0)","Low"
"type-fest","2.19.0","(MIT OR CC0-1.0)","Low"
"type-is","1.6.18","MIT","Low"
"typed-array-buffer","1.0.3","MIT","Low"
"typed-array-byte-length","1.0.3","MIT","Low"
"typed-array-byte-offset","1.0.4","MIT","Low"
"typed-array-length","1.0.7","MIT","Low"
"typedarray-to-buffer","3.1.5","MIT","Low"
"typedarray","0.0.6","MIT","Low"
"typescript","5.3.3","Apache-2.0","Low"
"typescript","5.4.5","Apache-2.0","Low"
"ua-parser-js","1.0.39","MIT","Low"
"uid","2.0.2","MIT","Low"
"ulid","2.3.0","MIT","Low"
"unbox-primitive","1.1.0","MIT","Low"
"undici-types","5.26.5","MIT","Low"
"unique-string","3.0.0","MIT","Low"
"universal-user-agent","6.0.1","ISC","Low"
"universalify","2.0.1","MIT","Low"
"unpipe","1.0.0","MIT","Low"
"untildify","4.0.0","MIT","Low"
"update-browserslist-db","1.1.3","MIT","Low"
"update-notifier","6.0.2","BSD-2-Clause","Low"
"uri-js","4.4.1","BSD-2-Clause","Low"
"url-join","5.0.0","MIT","Low"
"url-parse","1.5.10","MIT","Low"
"util-deprecate","1.0.2","MIT","Low"
"utils-merge","1.0.1","MIT","Low"
"uuid","9.0.1","MIT","Low"
"v8-compile-cache-lib","3.0.1","MIT","Low"
"v8-to-istanbul","9.2.0","ISC","Low"
"validator","13.12.0","MIT","Low"
"value-or-promise","1.0.12","MIT","Low"
"vary","1.1.2","MIT","Low"
"walker","1.0.8","Apache-2.0","Low"
"watchpack","2.4.2","MIT","Low"
"wcwidth","1.0.1","MIT","Low"
"web-streams-polyfill","3.3.3","MIT","Low"
"webidl-conversions","3.0.1","BSD-2-Clause","Low"
"webpack-node-externals","3.0.0","MIT","Low"
"webpack-sources","3.2.3","MIT","Low"
"webpack","5.90.1","MIT","Low"
"webpack","5.99.6","MIT","Low"
"whatwg-url","5.0.0","MIT","Low"
"which-boxed-primitive","1.1.1","MIT","Low"
"which-builtin-type","1.2.1","MIT","Low"
"which-collection","1.0.2","MIT","Low"
"which-typed-array","1.1.18","MIT","Low"
"which","2.0.2","ISC","Low"
"widest-line","3.1.0","MIT","Low"
"widest-line","4.0.1","MIT","Low"
"wildcard-match","5.1.2","ISC","Low"
"windows-release","5.1.1","MIT","Low"
"winston-transport","4.7.0","MIT","Low"
"winston","3.13.0","MIT","Low"
"word-wrap","1.2.5","MIT","Low"
"wrap-ansi","6.2.0","MIT","Low"
"wrap-ansi","7.0.0","MIT","Low"
"wrap-ansi","8.1.0","MIT","Low"
"wrappy","1.0.2","ISC","Low"
"write-file-atomic","3.0.3","ISC","Low"
"write-file-atomic","4.0.2","ISC","Low"
"ws","7.5.9","MIT","Low"
"ws","8.14.2","MIT","Low"
"xdg-basedir","5.1.0","MIT","Low"
"xtend","4.0.2","MIT","Low"
"y18n","5.0.8","ISC","Low"
"yallist","3.1.1","ISC","Low"
"yallist","4.0.0","ISC","Low"
"yargs-parser","21.1.1","ISC","Low"
"yargs","17.7.2","MIT","Low"
"yn","3.1.1","MIT","Low"
"yocto-queue","0.1.0","MIT","Low"