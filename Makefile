include Makefile-variables

# Variables
DOCKER_BASE_IMAGE="sales-hub-base"
DOCKER_NETWORK="sales-hub"
DOCKER_NETWORK_TEST="sales-hub-test"
DOCKER_COMPOSE_FILE_TEST="docker-compose.test.yml"

# Docker Compose command - use legacy syntax for production, modern syntax for other environments
ifeq ($(NODE_ENV), production)
  DOCKER_COMPOSE_CMD = docker-compose
else
  DOCKER_COMPOSE_CMD = docker compose
endif

# Helper variables for Docker run
DOCKER_RUN = docker run --rm --env-file .env --network ${DOCKER_NETWORK} --user $(id -u):$(id -g)
DOCKER_RUN_TEST = docker run --rm --env-file .env.test --network ${DOCKER_NETWORK_TEST} --user $(id -u):$(id -g)

all: | ${NODE_ENV}

############# envs
development: | docker-build-base docker-up-infra db-migrate db-seed rabbitmq-migrate blue-green-deploy  
production: | docker-build-base docker-up-network db-migrate rabbitmq-migrate blue-green-deploy
local: | docker-build-base docker-up-infra db-migrate db-seed rabbitmq-migrate docker-up 

############## Blue-Green Deployment
blue-green-deploy:
	./deploy/scripts/deploy-blue-green.sh

############# Docker Targets
docker-up-network:
	${DOCKER_COMPOSE_CMD} -f ${DOCKER_COMPOSE_FILE} up --no-start

docker-up:
	${DOCKER_COMPOSE_CMD} -f ${DOCKER_COMPOSE_FILE} up -d --force-recreate

docker-down:
	${DOCKER_COMPOSE_CMD} -f ${DOCKER_COMPOSE_FILE} down

docker-build-base:
	docker build --target base -t ${DOCKER_BASE_IMAGE} .

docker-up-infra: 
	./deploy/scripts/docker-up-infra.sh ${DOCKER_COMPOSE_FILE}

############# Docker Test Targets 
docker-down-test:
	${DOCKER_COMPOSE_CMD} -f ${DOCKER_COMPOSE_FILE_TEST} down

docker-up-infra-test:
	./deploy/scripts/docker-up-infra.sh ${DOCKER_COMPOSE_FILE_TEST}

############# Database Targets
db-migrate:
	${DOCKER_RUN} ${DOCKER_BASE_IMAGE} yarn migrate:prod

db-migrate-test:
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn migrate:test

db-seed:
	${DOCKER_RUN} ${DOCKER_BASE_IMAGE} yarn prisma db seed

############# RABBITMQ Targets
rabbitmq-migrate:
	${DOCKER_RUN} ${DOCKER_BASE_IMAGE} yarn rabbitmq:migrate

rabbitmq-migrate-test:
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn rabbitmq:migrate


# WITHOUT THIS, TEST DOES NOT RUN
.PHONY: test run-nest-command

############# Test Targets
test: | docker-build-base docker-up-infra-test db-migrate-test
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn test
	make docker-down-test

test-e2e: | docker-build-base docker-up-infra-test db-migrate-test rabbitmq-migrate-test  
	${DOCKER_RUN_TEST} ${DOCKER_BASE_IMAGE} yarn test:e2e
	make docker-down-test