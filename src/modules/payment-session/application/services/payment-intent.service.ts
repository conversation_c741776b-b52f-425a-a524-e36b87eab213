import { Inject, Injectable } from '@nestjs/common';

import { TotalWithFees } from '@/modules/fee/application/services/fee-calculator.application-service';
import { PaymentType } from '@/modules/payment/domain/payment.entity';
import {
  PaymentProviderServicePort,
  ProviderPaymentIntentCommonParams,
} from '@/modules/payment-provider/domain/payment-provider-service.port';
import { PaymentProviderAccountRepositoryPort } from '@/modules/payment-provider-account/domain/payment-provider-account-repository.port';
import { PaymentProviderAccountNotFoundError } from '@/modules/payment-provider-account/errors';
import { PaymentProviderCustomerService } from '@/modules/payment-provider-customer/application/services/payment-provider-customer.service';

interface CreatePaymentIntentForOrderParams {
  paymentProviderAccountId: string;
  totalWithFees: TotalWithFees;
  customerId: string;
  feeSettingsIdAtProvider: string | null;
}

interface UpdatePaymentIntentForOrderParams
  extends Omit<CreatePaymentIntentForOrderParams, 'customerId' | 'feeSettingsIdAtProvider'> {
  paymentIntentIdAtProvider: string;
}

@Injectable()
export class PaymentIntentService {
  constructor(
    @Inject(PaymentProviderServicePort)
    private readonly paymentProviderService: PaymentProviderServicePort,
    @Inject(PaymentProviderAccountRepositoryPort)
    private readonly paymentProviderAccountRepository: PaymentProviderAccountRepositoryPort,
    private readonly paymentProviderCustomerService: PaymentProviderCustomerService,
  ) {}

  async createPaymentIntentForOrder(params: CreatePaymentIntentForOrderParams) {
    const createPaymentIntentParams = await this.preparePaymentIntentCommonParams(params);

    const providerCustomer = await this.paymentProviderCustomerService.getPaymentProviderCustomer(
      params.customerId,
    );

    return this.paymentProviderService.createPaymentIntent({
      customerId: providerCustomer.getProps().idAtProvider,
      feeSettingsIdAtProvider: params.feeSettingsIdAtProvider,
      ...createPaymentIntentParams,
    });
  }

  async updatePaymentIntentForOrder(params: UpdatePaymentIntentForOrderParams) {
    const updatePaymentIntentParams = await this.preparePaymentIntentCommonParams(params);

    return this.paymentProviderService.updatePaymentIntent({
      id: params.paymentIntentIdAtProvider,
      ...updatePaymentIntentParams,
    });
  }

  private async preparePaymentIntentCommonParams({
    paymentProviderAccountId,
    totalWithFees,
  }: Omit<
    CreatePaymentIntentForOrderParams,
    'customerId' | 'feeSettingsIdAtProvider'
  >): Promise<ProviderPaymentIntentCommonParams> {
    const providerAccount = await this.getPaymentProviderAccount(paymentProviderAccountId);

    return {
      amount: totalWithFees.total,
      marketplaceFee: totalWithFees.marketplaceFee,
      paymentMethodType: PaymentType.CARD,
      paymentSplits: [
        {
          accountId: providerAccount.getProps().idAtProvider,
          amount: totalWithFees.netProfit,
        },
      ],
    };
  }

  private async getPaymentProviderAccount(paymentProviderAccountId: string) {
    const paymentProviderAccount =
      await this.paymentProviderAccountRepository.findById(paymentProviderAccountId);

    if (!paymentProviderAccount) {
      throw new PaymentProviderAccountNotFoundError();
    }

    return paymentProviderAccount;
  }
}
