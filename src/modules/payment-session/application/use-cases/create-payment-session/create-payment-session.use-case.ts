import { Inject, Injectable } from '@nestjs/common';
import { checkRules, UseCase } from '@sw-web/nestjs-core/domain';
import { sumBy } from 'lodash';

import { FeeCalculatorApplicationService } from '@/modules/fee/application/services/fee-calculator.application-service';
import { OrderRepositoryPort } from '@/modules/order/domain/order-repository.port';
import { OrderEntity } from '@/modules/order/domain/order.entity';
import { OrderNotFoundError } from '@/modules/order/errors';
import { PaymentProviderPaymentType } from '@/modules/payment-provider/domain/payment-provider.entity';
import { PaymentSessionRepositoryPort } from '@/modules/payment-session/domain/payment-session-repository.port';
import { PaymentSessionEntity } from '@/modules/payment-session/domain/payment-session.entity';
import { PaymentSessionTotalAndFeesMustMatchRule } from '@/modules/payment-session/domain/rules';
import { PaymentSessionDto } from '@/modules/payment-session/presentation/dto/payment-session.dto';
import { PointOfSaleRepositoryPort } from '@/modules/point-of-sale/domain/point-of-sale-repository.port';
import { PointOfSaleNotFoundError } from '@/modules/point-of-sale/errors';
import { PosPaymentProviderAccountService } from '@/modules/pos-payment-provider-account/application/services/pos-payment-provider-account.service';
import { ClientInfo } from '@/modules/shared/interfaces/client-info.interface';

import { PaymentSessionMapper } from '../../mappers/payment-session.mapper';
import { PaymentIntentService } from '../../services/payment-intent.service';

interface Props {
  orderId: string;
  total: number;
  marketplaceFee: number;
  paymentFee: number;
  paymentProviderPaymentType: PaymentProviderPaymentType;
  clientInfo: ClientInfo;
}

@Injectable()
export class CreatePaymentSessionUseCase implements UseCase<Props, PaymentSessionDto> {
  constructor(
    @Inject(PaymentSessionRepositoryPort)
    private readonly paymentSessionRepository: PaymentSessionRepositoryPort,
    @Inject(OrderRepositoryPort)
    private readonly orderRepository: OrderRepositoryPort,
    @Inject(PointOfSaleRepositoryPort)
    private readonly pointOfSaleRepository: PointOfSaleRepositoryPort,
    private readonly paymentIntentService: PaymentIntentService,
    private readonly feeCalculatorService: FeeCalculatorApplicationService,
    private readonly posPaymentProviderAccountService: PosPaymentProviderAccountService,
    private readonly mapper: PaymentSessionMapper,
  ) {}

  async execute(props: Props) {
    const order = await this.getOrder(props.orderId);

    const posPaymentProviderAccount =
      await this.posPaymentProviderAccountService.getByPointOfSaleId(
        order.getProps().pointOfSaleId,
      );

    const pointOfSale = await this.getPointOfSale(order.getProps().pointOfSaleId);

    const totalWithFees = this.feeCalculatorService.calculateTotalWithFees({
      subtotal: order.getProps().currentAmount,
      marketplaceFee: this.calculateOrderMarketplaceFee(order),
      paymentMethodType: props.paymentProviderPaymentType,
      marketplaceFeePayer: pointOfSale.getProps().marketplaceFeePayer,
      paymentFeePayer: posPaymentProviderAccount.getProps().paymentProviderFeePayer,
      customFeeFixed: posPaymentProviderAccount.getProps().paymentProviderFeeFixed,
      customFeePercentage: posPaymentProviderAccount.getProps().paymentProviderFeePercentage,
    });

    checkRules(
      new PaymentSessionTotalAndFeesMustMatchRule(
        props.total,
        props.marketplaceFee,
        props.paymentFee,
        totalWithFees,
      ),
    );

    const paymentIntentAtProvider = await this.paymentIntentService.createPaymentIntentForOrder({
      totalWithFees,
      paymentProviderAccountId: posPaymentProviderAccount.getProps().paymentProviderAccountId,
      customerId: order.getProps().customerId,
      feeSettingsIdAtProvider: posPaymentProviderAccount.getProps().feeSettingsIdAtProvider,
    });

    const paymentSession = PaymentSessionEntity.create({
      orderId: props.orderId,
      paymentIntentIdAtProvider: paymentIntentAtProvider.id,
      paymentProviderType: paymentIntentAtProvider.providerType,
      paymentProviderPaymentType: props.paymentProviderPaymentType,
      amount: paymentIntentAtProvider.amount,
      marketplaceFeePayer: pointOfSale.getProps().marketplaceFeePayer,
      paymentFeePayer: posPaymentProviderAccount.getProps().paymentProviderFeePayer,
      paymentFee: totalWithFees.paymentFee,
      marketplaceFee: totalWithFees.marketplaceFee,
      clientInfo: props.clientInfo,
      providerFeeFixed: totalWithFees.feeSettings.feeFixed,
      providerFeePercentage: totalWithFees.feeSettings.feePercentage,
    });

    await this.paymentSessionRepository.create(paymentSession);

    return this.mapper.toUi(paymentSession);
  }

  private calculateOrderMarketplaceFee(order: OrderEntity) {
    const { orderItems } = order.getProps();

    return sumBy(
      orderItems,
      (orderItem) => orderItem.getProps().currentQuantity * orderItem.getProps().marketplaceFee,
    );
  }

  private async getPointOfSale(posId: string) {
    const pointOfSale = await this.pointOfSaleRepository.findById(posId);

    if (!pointOfSale) {
      throw new PointOfSaleNotFoundError();
    }

    return pointOfSale;
  }

  private async getOrder(orderId: string) {
    const order = await this.orderRepository.findById(orderId);

    if (!order) {
      throw new OrderNotFoundError();
    }

    return order;
  }
}
