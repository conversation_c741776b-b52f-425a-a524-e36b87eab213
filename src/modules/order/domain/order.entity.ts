import { Entity } from '@sw-web/nestjs-core/domain';

import { OrderCustomFieldEntity } from './order-custom-field.entity';
import { OrderItemEntity } from './order-item.entity';

export enum OrderStatus {
  CANCELED = 'canceled',
  AWAITING_PAYMENT = 'awaiting-payment',
  PAID = 'paid',
}

interface OrderCustomerInfo {
  phone: string;
  email: string;
  country: string;
  firstName: string;
  lastName: string;
  zip: string | null;
}

interface OrderRecipientInfo {
  email: string;
}

interface Props {
  customerId: string;
  customerInfo: OrderCustomerInfo;
  status: OrderStatus;
  initialAmount: number;
  currentAmount: number;
  subtotal: number;
  entryCode: string | null;
  orderItems: OrderItemEntity[];
  pointOfSaleId: string;
  customFields: OrderCustomFieldEntity[];
  marketplaceFee: number;
  recipientInfo: OrderRecipientInfo;
}

export class OrderEntity extends Entity<Props> {
  static create(props: Props) {
    return new OrderEntity({ props });
  }
}
