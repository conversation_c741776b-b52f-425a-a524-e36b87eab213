import { IRepository } from '@sw-web/nestjs-core/domain';

import { OrderEntity } from './order.entity';

export interface FindPaidOrdersForDuplicateCheckFilters {
  fingerprintAtProvider: string;
  email: string;
  phone: string;
}
export interface OrderRepositoryPort extends IRepository<OrderEntity> {
  findPaidOrdersByPosId(
    posId: string,
    filters: FindPaidOrdersForDuplicateCheckFilters,
  ): Promise<OrderEntity[]>;
}

export const OrderRepositoryPort: unique symbol = Symbol('ORDER_REPOSITORY');
