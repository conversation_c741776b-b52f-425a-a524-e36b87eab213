import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  ValidateNested,
} from 'class-validator';

import { IsUlid } from '@/core/infra';

import { OrderItemHolderDto } from './order.dto';

class OrderItemDto {
  @ApiProperty({ description: 'Order item quantity' })
  @IsNumber()
  quantity: number;

  @ApiProperty({ description: 'ID of product' })
  @IsUlid()
  productId: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => OrderItemHolderDto)
  holder?: OrderItemHolderDto | null;
}

class CustomerDto {
  @ApiProperty({ description: 'Customer first name' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'Customer last name' })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'Customer zip' })
  @IsString()
  @IsOptional()
  zip?: string | null;

  @ApiProperty({ description: 'Customer phone' })
  @IsPhoneNumber()
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ description: 'Customer email' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'Customer country' })
  @IsString()
  @IsNotEmpty()
  country: string;
}

class RecipientDto {
  @ApiProperty({ description: 'Customer email' })
  @IsEmail()
  @IsOptional()
  email?: string | null;
}

export class CreateOrderDto {
  @ApiProperty({ description: 'ID of point of sale' })
  @IsUlid()
  pointOfSaleId: string;

  @ApiProperty({ description: 'Order amount' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Total marketplace fee' })
  @IsNumber()
  marketplaceFee: number;

  @ArrayMinSize(1)
  @ValidateNested()
  @Type(() => OrderItemDto)
  orderItems: OrderItemDto[];

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CustomerDto)
  customer: CustomerDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => RecipientDto)
  recipient?: RecipientDto | null;

  @IsOptional()
  @ValidateNested()
  @Type(() => CustomFieldResponseDto)
  customFieldResponses: CustomFieldResponseDto[];

  @IsOptional()
  @IsString()
  entryCode?: string | null;
}

export class CustomFieldResponseDto {
  @ApiProperty({ description: 'Custom Field Id' })
  @IsUlid()
  customFieldId: string;

  @ApiProperty({ description: 'Custom field response value' })
  @IsNotEmpty()
  @IsString()
  value: string;
}
