import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';

import { MarketplaceApiKeyGuard } from '@/infra/guards';

import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { CreateOrderUseCase } from '../application/use-cases/create-order/create-order.use-case';
import { GetOrderUseCase } from '../application/use-cases/get-order/get-order.use-case';
import { MarkOrderPaidUseCase } from '../application/use-cases/mark-order-paid/mark-order-paid.use-case';
import { UpdateOrderUseCase } from '../application/use-cases/update-order/update-order.use-case';

@Controller('orders')
export class OrderController {
  constructor(
    private readonly createOrder: CreateOrderUseCase,
    private readonly updateOrder: UpdateOrderUseCase,
    private readonly getOrder: GetOrderUseCase,
    private readonly markOrderPaid: MarkOrderPaidUseCase,
  ) {}

  @Post()
  public async create(@Body() { customFieldResponses, ...dto }: CreateOrderDto) {
    return this.createOrder.execute({
      ...dto,
      customFields: customFieldResponses,
      entryCode: dto.entryCode || null,
      recipient: {
        email: dto.recipient?.email,
      },
    });
  }

  // TODO: ADD ACCESS CONTROL
  @UseGuards(MarketplaceApiKeyGuard)
  @Put(':orderId')
  public async update(
    @Body() { customFieldResponses, ...dto }: UpdateOrderDto,
    @Param('orderId') orderId: string,
  ) {
    return this.updateOrder.execute({
      ...dto,
      customFields: customFieldResponses,
      orderId,
      recipient: {
        email: dto.recipient?.email,
      },
    });
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Get(':orderId')
  public async get(@Param('orderId') orderId: string) {
    return this.getOrder.execute({
      orderId,
    });
  }

  @Put(':orderId/mark-paid')
  async markPaid(@Param('orderId') orderId: string) {
    return this.markOrderPaid.execute({ orderId });
  }
}
