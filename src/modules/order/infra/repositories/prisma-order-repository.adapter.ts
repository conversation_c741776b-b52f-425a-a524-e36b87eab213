/* eslint-disable @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import {
  Prisma,
  OrderItem as OrderItemDbRecord,
  OrderItemHolder as OrderItemHolderDbRecord,
  OrderCustomField as OrderCustomFieldDbRecord,
} from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Paginated, PaginatedQueryParams, RepositoryBase } from '@sw-web/nestjs-core/domain';

import { PrismaService } from '@/infra/drivers/prisma/prisma.service';

import { OrderMapper } from '../../application/mappers/order.mapper';
import { OrderCustomFieldEntity } from '../../domain/order-custom-field.entity';
import { OrderItemHolderEntity } from '../../domain/order-item-holder.entity';
import { OrderItemEntity } from '../../domain/order-item.entity';
import {
  FindPaidOrdersForDuplicateCheckFilters,
  OrderRepositoryPort,
} from '../../domain/order-repository.port';
import { OrderEntity, OrderStatus } from '../../domain/order.entity';

const OrderPrismaValidator = Prisma.validator<Prisma.OrderDefaultArgs>()({
  include: {
    orderItems: {
      include: {
        itemHolder: true,
      },
    },
    customFields: true,
  },
});

export type OrderDbRecord = Prisma.OrderGetPayload<typeof OrderPrismaValidator>;

@Injectable()
export class PrismaOrderRepositoryAdapter
  extends RepositoryBase<OrderEntity>
  implements OrderRepositoryPort
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly mapper: OrderMapper,
  ) {
    super(prisma);
  }

  async findPaidOrdersByPosId(
    posId: string,
    filters: FindPaidOrdersForDuplicateCheckFilters,
  ): Promise<OrderEntity[]> {
    const orders = await this.prisma.order.findMany({
      ...OrderPrismaValidator,
      where: {
        pointOfSaleId: posId,
        status: OrderStatus.PAID,
        OR: [
          {
            payments: {
              some: {
                paymentMethod: {
                  fingerprintAtProvider: filters.fingerprintAtProvider,
                },
              },
            },
          },
          {
            customerEmail: filters.email,
          },
          {
            customerPhone: filters.phone,
          },
        ],
      },
    });

    return orders.map((x) => this.mapper.toDomain(x));
  }

  async create(order: OrderEntity): Promise<void> {
    await this.prisma.$transaction(async () => {
      await this.prisma.order.create({
        data: this.mapToOrderDbRecord(order),
      });

      const orderItems = order
        .getProps()
        .orderItems.map((orderItem) => this.mapToOrderItemDbRecord(order.getId(), orderItem));

      await this.prisma.orderItem.createMany({
        data: orderItems,
      });

      const orderItemHolders = order
        .getProps()
        .orderItems.filter((orderItem) => !!orderItem.getProps().holder)
        .map((orderItem) =>
          this.mapToOrderItemHolderDbRecord(
            orderItem.getId(),
            orderItem.getProps().holder as OrderItemHolderEntity,
          ),
        );

      const orderCustomFields = order
        .getProps()
        .customFields.map((field) => this.mapToOrderCustomFieldDbRecord(order.getId(), field));

      await this.prisma.orderItemHolder.createMany({
        data: orderItemHolders,
      });

      await this.prisma.orderCustomField.createMany({
        data: orderCustomFields,
      });
    });
  }

  private mapToOrderCustomFieldDbRecord(
    orderId: string,
    field: OrderCustomFieldEntity,
  ): OrderCustomFieldDbRecord {
    const props = field.getProps();

    return {
      orderCustomFieldId: props.id,
      customFieldId: props.customFieldId,
      value: props.value,
      orderId,
      createdAt: props.createdAt,
      modifiedAt: props.updatedAt,
    };
  }

  private mapToOrderDbRecord(
    order: OrderEntity,
  ): Omit<OrderDbRecord, 'orderItems' | 'customFields'> {
    const props = order.getProps();

    return {
      orderId: props.id,
      customerCountry: props.customerInfo.country,
      customerEmail: props.customerInfo.email,
      customerFirstName: props.customerInfo.firstName,
      customerLastName: props.customerInfo.lastName,
      customerPhone: props.customerInfo.phone,
      customerZip: props.customerInfo.zip,
      recipientEmail: props.recipientInfo.email,
      pointOfSaleId: props.pointOfSaleId,
      initialAmount: new Decimal(props.initialAmount),
      currentAmount: new Decimal(props.currentAmount),
      subtotal: new Decimal(props.subtotal),
      entryCode: props.entryCode,
      customerId: props.customerId,
      status: props.status,
      marketplaceFee: new Decimal(props.marketplaceFee),
      createdAt: props.createdAt,
      modifiedAt: props.updatedAt,
    };
  }

  private mapToOrderItemDbRecord(orderId: string, orderItem: OrderItemEntity): OrderItemDbRecord {
    const props = orderItem.getProps();

    return {
      orderItemId: props.id,
      orderId,
      productId: props.productId,
      price: new Decimal(props.price),
      currentQuantity: props.currentQuantity,
      initialQuantity: props.initialQuantity,
      initialTotalAmount: new Decimal(props.initialTotalAmount),
      currentTotalAmount: new Decimal(props.currentTotalAmount),
      subtotalAmount: new Decimal(props.subtotalAmount),
      initialTotalMarketplaceFee: new Decimal(props.initialTotalMarketplaceFee),
      currentTotalMarketplaceFee: new Decimal(props.currentTotalMarketplaceFee),
      marketplaceFee: new Decimal(props.marketplaceFee),
      createdAt: props.createdAt,
      modifiedAt: props.updatedAt,
    };
  }

  private mapToOrderItemHolderDbRecord(
    orderItemId: string,
    holder: OrderItemHolderEntity,
  ): OrderItemHolderDbRecord {
    const props = holder.getProps();

    return {
      orderItemHolderId: props.id,
      orderItemId,
      firstName: props.firstName,
      lastName: props.lastName,
      address: props.address,
      zip: props.zip,
      phone: props.phone,
      email: props.email,
      country: props.country,
      state: props.state,
      createdAt: props.createdAt,
      modifiedAt: props.updatedAt,
    };
  }

  async update(order: OrderEntity): Promise<void> {
    await this.prisma.$transaction(async () => {
      await this.prisma.order.update({
        data: this.mapToOrderDbRecord(order),
        where: {
          orderId: order.getId(),
        },
      });

      const updateOrderItems = order.getProps().orderItems.map(async (orderItem) =>
        this.prisma.orderItem.upsert({
          create: this.mapToOrderItemDbRecord(order.getId(), orderItem),
          update: this.mapToOrderItemDbRecord(order.getId(), orderItem),
          where: {
            orderItemId: orderItem.getId(),
          },
        }),
      );

      await this.updateOrderItemHolders(order.getProps().orderItems);

      await this.updateOrderCustomFields(order.getId(), order.getProps().customFields);

      const updatedOrderItems = await Promise.all(updateOrderItems);

      await this.prisma.orderItem.deleteMany({
        where: {
          orderId: order.getId(),
          orderItemId: {
            notIn: updatedOrderItems.map(({ orderItemId }) => orderItemId),
          },
        },
      });
    });
  }

  private async updateOrderItemHolders(orderItems: OrderItemEntity[]) {
    const itemsWithoutHolder = orderItems.filter((item) => !item.getProps().holder);
    const itemsWithHolder = orderItems.filter((item) => !!item.getProps().holder);

    const updateHolders = itemsWithHolder.map(async (item) =>
      this.prisma.orderItemHolder.upsert({
        create: this.mapToOrderItemHolderDbRecord(
          item.getId(),
          item.getProps().holder as OrderItemHolderEntity,
        ),
        update: this.mapToOrderItemHolderDbRecord(
          item.getId(),
          item.getProps().holder as OrderItemHolderEntity,
        ),
        where: {
          orderItemId: item.getId(),
        },
      }),
    );

    const deleteHolders = this.prisma.orderItemHolder.deleteMany({
      where: {
        orderItemId: {
          in: itemsWithoutHolder.map((item) => item.getId()),
        },
      },
    });

    await Promise.all([...updateHolders, deleteHolders]);
  }

  private async updateOrderCustomFields(orderId: string, customFields: OrderCustomFieldEntity[]) {
    const updateCustomFields = customFields.map(async (field) =>
      this.prisma.orderCustomField.upsert({
        create: this.mapToOrderCustomFieldDbRecord(orderId, field),
        update: this.mapToOrderCustomFieldDbRecord(orderId, field),
        where: {
          orderCustomFieldId: field.getId(),
        },
      }),
    );

    const updatedCustomFields = await Promise.all(updateCustomFields);

    await this.prisma.orderCustomField.deleteMany({
      where: {
        orderId,
        orderCustomFieldId: {
          notIn: updatedCustomFields.map((field) => field.orderCustomFieldId),
        },
      },
    });
  }

  async delete(entity: OrderEntity): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<OrderEntity | null> {
    const order = await this.prisma.order.findFirst({
      ...OrderPrismaValidator,
      where: {
        orderId: id,
      },
    });

    return order && this.mapper.toDomain(order);
  }

  async findAll(): Promise<OrderEntity[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<OrderEntity>> {
    throw new Error('Method not implemented.');
  }
}
