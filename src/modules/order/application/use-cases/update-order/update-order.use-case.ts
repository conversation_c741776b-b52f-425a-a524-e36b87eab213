import { Inject, Injectable } from '@nestjs/common';
import { UseCase, checkRules } from '@sw-web/nestjs-core/domain';
import { EventBus } from '@sw-web/nestjs-core/event-bus';

import { CustomFieldResponse } from '@/modules/custom-field/domain/services/custom-field.domain-service';
import { OrderUpdatedEvent } from '@/modules/order/domain/events/order-updated.event';
import { OrderRepositoryPort } from '@/modules/order/domain/order-repository.port';
import {
  OrderCantHaveFreeAndNonFreeProductsRule,
  OrderTotalAndFeesMustMatchRule,
} from '@/modules/order/domain/rules';
import { OrderNotFoundError } from '@/modules/order/errors';
import { OrderDto, OrderItemHolderDto } from '@/modules/order/presentation/dto/order.dto';

import { OrderMapper } from '../../mappers/order.mapper';
import { OrderCustomFieldApplicationService } from '../../services/order-custom-field.application-service';
import { OrderItemApplicationService } from '../../services/order-item.service';

interface Props {
  orderId: string;
  pointOfSaleId: string;
  amount: number;
  marketplaceFee: number;
  orderItems: {
    quantity: number;
    productId: string;
    holder?: OrderItemHolderDto | null;
  }[];
  recipient?: {
    email?: string | null;
  };
  customFields?: CustomFieldResponse[];
}

@Injectable()
export class UpdateOrderUseCase implements UseCase<Props, OrderDto> {
  constructor(
    @Inject(OrderRepositoryPort)
    private readonly orderRepository: OrderRepositoryPort,
    private readonly orderMapper: OrderMapper,
    private readonly orderItemService: OrderItemApplicationService,
    private readonly orderCustomFieldApplicationService: OrderCustomFieldApplicationService,
    private readonly eventBus: EventBus,
  ) {}

  async execute(props: Props) {
    const order = await this.orderRepository.findById(props.orderId);

    if (!order) {
      throw new OrderNotFoundError();
    }

    const orderItems = await this.orderItemService.getValidatedOrderItems(props.orderItems);

    checkRules(
      new OrderTotalAndFeesMustMatchRule(orderItems, props.amount, props.marketplaceFee),
      new OrderCantHaveFreeAndNonFreeProductsRule(orderItems),
    );

    order.update({
      pointOfSaleId: props.pointOfSaleId,
      currentAmount: props.amount,
      orderItems,
      customFields: await this.orderCustomFieldApplicationService.getValidatedOrderCustomFields({
        pointOfSaleId: props.pointOfSaleId,
        customFieldResponses: props.customFields || [],
      }),
    });

    if (props.recipient?.email) {
      order.update({
        recipientInfo: {
          email: props.recipient.email,
        },
      });
    }

    await this.orderRepository.transaction(async () => {
      await this.orderRepository.update(order);

      await this.eventBus.publish(new OrderUpdatedEvent({ order }));
    });

    return this.orderMapper.toUi(order);
  }
}
