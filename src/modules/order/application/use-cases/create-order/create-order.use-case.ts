import { Inject, Injectable } from '@nestjs/common';
import { UseCase, checkRules } from '@sw-web/nestjs-core/domain';
import { EventBus } from '@sw-web/nestjs-core/event-bus';

import { CustomFieldResponse } from '@/modules/custom-field/domain/services/custom-field.domain-service';
import { CustomerApplicationService } from '@/modules/customer/application/services/customer.application-service';
import { OrderCreatedEvent } from '@/modules/order/domain/events/order-created.event';
import { OrderRepositoryPort } from '@/modules/order/domain/order-repository.port';
import { OrderEntity, OrderStatus } from '@/modules/order/domain/order.entity';
import {
  OrderCantHaveFreeAndNonFreeProductsRule,
  OrderTotalAndFeesMustMatchRule,
} from '@/modules/order/domain/rules';
import { OrderDto, OrderItemHolderDto } from '@/modules/order/presentation/dto/order.dto';
import { PointOfSaleApplicationService } from '@/modules/point-of-sale/application/services/point-of-sale.application-service';
import { EntryCodeValidatorServicePort } from '@/modules/point-of-sale/domain/entry-code-validator-service.port';
import { PointOfSaleEntity } from '@/modules/point-of-sale/domain/point-of-sale.entity';
import { InvalidEntryCodeError } from '@/modules/point-of-sale/errors';

import { OrderMapper } from '../../mappers/order.mapper';
import { OrderCustomFieldApplicationService } from '../../services/order-custom-field.application-service';
import { OrderItemApplicationService } from '../../services/order-item.service';

interface Props {
  pointOfSaleId: string;
  amount: number;
  marketplaceFee: number;
  orderItems: {
    quantity: number;
    productId: string;
    holder?: OrderItemHolderDto | null;
  }[];
  customer: {
    firstName: string;
    lastName: string;
    phone: string;
    zip?: string | null;
    email: string;
    country: string;
  };
  recipient?: {
    email?: string | null;
  };
  customFields?: CustomFieldResponse[];
  entryCode: string | null;
}

@Injectable()
export class CreateOrderUseCase implements UseCase<Props, OrderDto> {
  constructor(
    @Inject(OrderRepositoryPort)
    private readonly orderRepository: OrderRepositoryPort,
    private readonly customerApplicationService: CustomerApplicationService,
    private readonly orderMapper: OrderMapper,
    private readonly orderItemService: OrderItemApplicationService,
    private readonly orderCustomFieldApplicationService: OrderCustomFieldApplicationService,
    private readonly pointOfSaleApplicationService: PointOfSaleApplicationService,
    @Inject(EntryCodeValidatorServicePort)
    private readonly entryCodeValidator: EntryCodeValidatorServicePort,
    private readonly eventBus: EventBus,
  ) {}

  async execute(props: Props) {
    const customer = await this.customerApplicationService.createOrUpdateCustomer(props.customer);

    const orderItems = await this.orderItemService.getValidatedOrderItems(props.orderItems);

    const pointOfSale = await this.pointOfSaleApplicationService.getActivePublishedPointOfSales(
      props.pointOfSaleId,
    );

    await this.validateEntryCode(pointOfSale, props.entryCode);

    checkRules(
      new OrderTotalAndFeesMustMatchRule(orderItems, props.amount, props.marketplaceFee),
      new OrderCantHaveFreeAndNonFreeProductsRule(orderItems),
    );

    const order = OrderEntity.create({
      pointOfSaleId: props.pointOfSaleId,
      customerInfo: {
        ...props.customer,
        zip: props.customer.zip || null,
      },
      recipientInfo: {
        email: props.recipient?.email || props.customer.email,
      },
      customerId: customer.getId(),
      status: OrderStatus.AWAITING_PAYMENT,
      // Amount will be updated after payment is made, since fee is available only after payment
      initialAmount: props.amount,
      currentAmount: props.amount,
      subtotal: props.amount,
      orderItems,
      customFields: await this.orderCustomFieldApplicationService.getValidatedOrderCustomFields({
        pointOfSaleId: props.pointOfSaleId,
        customFieldResponses: props.customFields || [],
      }),
      entryCode: props.entryCode,
      marketplaceFee: props.marketplaceFee,
    });

    await this.orderRepository.transaction(async () => {
      await this.orderRepository.create(order);

      await this.eventBus.publish(new OrderCreatedEvent({ order }));
    });

    return this.orderMapper.toUi(order);
  }

  private async validateEntryCode(pointOfSale: PointOfSaleEntity, entryCode: string | null) {
    if (!pointOfSale.getProps().entryCodeRequired) {
      return;
    }

    if (!entryCode) {
      throw new InvalidEntryCodeError();
    }

    const result = await this.entryCodeValidator.validate(pointOfSale.getId(), entryCode);

    if (!result.isValid) {
      throw new InvalidEntryCodeError();
    }
  }
}
