import { OrderItemHolder as OrderItemHolderDbRecord } from '@prisma/client';
import { Mapper } from '@sw-web/nestjs-core/domain';

import { OrderCustomFieldEntity } from '../../domain/order-custom-field.entity';
import { OrderItemHolderEntity } from '../../domain/order-item-holder.entity';
import { OrderItemEntity } from '../../domain/order-item.entity';
import { OrderEntity, OrderStatus } from '../../domain/order.entity';
import { OrderDbRecord } from '../../infra/repositories/prisma-order-repository.adapter';
import { OrderItemHolderDto, OrderDto as OrderUi } from '../../presentation/dto/order.dto';

export class OrderMapper implements Mapper<OrderEntity, OrderDbRecord, OrderUi> {
  toDomain(record: OrderDbRecord): OrderEntity {
    const { orderItems, customFields } = record;

    return new OrderEntity({
      id: record.orderId,
      createdAt: record.createdAt,
      updatedAt: record.modifiedAt,
      props: {
        pointOfSaleId: record.pointOfSaleId,
        customerId: record.customerId,
        customerInfo: {
          phone: record.customerPhone,
          email: record.customerEmail,
          country: record.customerCountry,
          firstName: record.customerFirstName,
          lastName: record.customerLastName,
          zip: record.customerZip,
        },
        recipientInfo: {
          email: record.recipientEmail,
        },
        status: record.status as OrderStatus,
        initialAmount: record.initialAmount.toNumber(),
        currentAmount: record.currentAmount.toNumber(),
        subtotal: record.subtotal.toNumber(),
        entryCode: record.entryCode,
        orderItems: orderItems.map((x) => this.mapOrderItemDomain(x)),
        customFields: customFields.map(
          (customField) =>
            new OrderCustomFieldEntity({
              id: customField.orderCustomFieldId,
              props: {
                customFieldId: customField.customFieldId,
                value: customField.value,
              },
              createdAt: customField.createdAt,
              updatedAt: customField.modifiedAt,
            }),
        ),
        marketplaceFee: record.marketplaceFee.toNumber(),
      },
    });
  }

  toUi(entity: OrderEntity): OrderUi {
    const props = entity.getProps();

    return {
      id: props.id,
      pointOfSaleId: props.pointOfSaleId,
      amount: props.currentAmount,
      customerId: props.customerId,
      status: props.status,
      orderItems: props.orderItems.map((x) => this.mapOrderItemUi(x)),
    };
  }

  private mapOrderItemDomain(orderItem: OrderDbRecord['orderItems'][number]): OrderItemEntity {
    const { itemHolder } = orderItem;

    return new OrderItemEntity({
      id: orderItem.orderItemId,
      createdAt: orderItem.createdAt,
      updatedAt: orderItem.modifiedAt,
      props: {
        price: orderItem.price.toNumber(),
        initialQuantity: orderItem.initialQuantity,
        currentQuantity: orderItem.currentQuantity,
        subtotalAmount: orderItem.subtotalAmount.toNumber(),
        initialTotalAmount: orderItem.initialTotalAmount.toNumber(),
        currentTotalAmount: orderItem.currentTotalAmount.toNumber(),
        initialTotalMarketplaceFee: orderItem.initialTotalMarketplaceFee.toNumber(),
        currentTotalMarketplaceFee: orderItem.currentTotalMarketplaceFee.toNumber(),
        productId: orderItem.productId,
        marketplaceFee: orderItem.marketplaceFee.toNumber(),
        holder: itemHolder && this.mapOrderItemHolder(itemHolder),
      },
    });
  }

  private mapOrderItemHolder(holder: OrderItemHolderDbRecord) {
    return new OrderItemHolderEntity({
      id: holder.orderItemHolderId,
      createdAt: holder.createdAt,
      updatedAt: holder.modifiedAt,
      props: {
        firstName: holder.firstName,
        lastName: holder.lastName,
        address: holder.address,
        zip: holder.zip,
        phone: holder.phone,
        email: holder.email,
        country: holder.country,
        state: holder.state,
      },
    });
  }

  private mapOrderItemUi(orderItem: OrderItemEntity): OrderUi['orderItems'][number] {
    const props = orderItem.getProps();

    return {
      id: props.id,
      price: props.price,
      quantity: props.currentQuantity,
      productId: props.productId,
      marketplaceFee: props.marketplaceFee,
      holder: props.holder && this.mapOrderItemHolderUi(props.holder),
    };
  }

  private mapOrderItemHolderUi(holder: OrderItemHolderEntity): OrderItemHolderDto {
    const holderProps = holder.getProps();
    return {
      firstName: holderProps.firstName,
      lastName: holderProps.lastName,
      address: holderProps.address,
      zip: holderProps.zip,
      phone: holderProps.phone,
      email: holderProps.email,
      country: holderProps.country,
      state: holderProps.state,
    };
  }
}
