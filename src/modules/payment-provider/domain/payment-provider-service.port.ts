import { PaymentProviderPaymentType, PaymentProviderType } from './payment-provider.entity';

export enum PaymentHubGatewayType {
  STRIPE = 'stripe',
}

export interface CreateProviderAccountParams {
  idAtProvider: string;
  name?: string;
  email?: string;
}

export interface CreateProviderCustomerParams {
  name?: string;
  email?: string;
}

type UpdateProviderAccountParams = CreateProviderAccountParams;

export interface ProviderPaymentIntentCommonParams {
  amount: number;
  marketplaceFee: number;
  paymentSplits: {
    accountId: string;
    amount: number;
  }[];
  statementDescriptor?: string;
  metadata?: Record<string, unknown>;
  paymentMethodType: PaymentProviderPaymentType;
}
export interface CreateProviderPaymentIntentParams extends ProviderPaymentIntentCommonParams {
  customerId: string;
  feeSettingsIdAtProvider?: string | null;
}

export type UpdateProviderPaymentIntentParams = ProviderPaymentIntentCommonParams & {
  id: string;
};

export interface CreateProviderRefundParams {
  paymentId: string;
  amount: number;
  refundSplits: { accountId: string; amount: number }[];
  currency: string;
  metadata?: Record<string, string>;
  marketplaceOrderFee: number;
  platformOrderFee: number;
}

interface CommonResult {
  providerType: PaymentProviderType;
}

export interface ProviderCustomerResult extends CommonResult {
  id: string;
  name: string | null;
}

export interface ProviderAccountResult extends CommonResult {
  id: string;
  name: string | null;
  email: string | null;
}

export interface ProviderRefundResult extends CommonResult {
  id: string;
  amount: number;
  currency: string;
}

export interface ProviderPaymentIntentResult extends CommonResult {
  id: string;
  amount: number;
  currency: string;
  paymentMethodType: string;
  // TEMPORARY WORKAROUND TO GET STRIPE CHARGE UPON SENDING WEBHOOK TO MARKETPLACE
  paymentIntentIdAtGateway: string;
}

export interface ProviderFeeSettingsResult extends CommonResult {
  id: string;
  marketplaceFeePercentage: number;
  marketplaceFeeFixed: number;
  isDefault: boolean;
}

export interface CalculatePaymentProviderFeeParams {
  amount: number;
  paymentMethodType: PaymentProviderPaymentType;
  /**
   * If true, then fee is calculated using default formula `amount * feePercentage + feeFixed`
   * else, will be calculated so that customer covers fee `(amount + feeFixed)/(1 - feePercentage)`
   */
  isFeeIncluded: boolean;
  customFeeFixed?: number | null;
  customFeePercentage?: number | null;
}

export interface CreateProviderFeeSettingsParams {
  marketplaceFeePercentage: number;
  marketplaceFeeFixed: number;
  isDefault?: boolean;
}

export type UpdateProviderFeeSettingsParams = CreateProviderFeeSettingsParams & {
  feeSettingsIdAtProvider: string;
};

export interface ProviderFeeSettings {
  fixed: number;
  percentage: number;
}

export interface ProviderCalculatedFee {
  fee: number;
  feeFixed: number;
  feePercentage: number;
}
export interface PaymentProviderCurrency {
  USD: string;
}

export interface PaymentProviderServicePort {
  get providerType(): PaymentProviderType;
  get cardDefaultFee(): ProviderFeeSettings;
  get currency(): PaymentProviderCurrency;

  calculateFee(params: CalculatePaymentProviderFeeParams): ProviderCalculatedFee;

  createFeeSettings(params: CreateProviderFeeSettingsParams): Promise<ProviderFeeSettingsResult>;

  updateFeeSettings(params: UpdateProviderFeeSettingsParams): Promise<ProviderFeeSettingsResult>;

  createCustomer(params: CreateProviderCustomerParams): Promise<ProviderCustomerResult>;

  createAccount(params: CreateProviderAccountParams): Promise<ProviderAccountResult>;

  createRefund(params: CreateProviderRefundParams): Promise<ProviderRefundResult>;

  updateAccount(params: UpdateProviderAccountParams): Promise<ProviderAccountResult>;

  createPaymentIntent(
    params: CreateProviderPaymentIntentParams,
  ): Promise<ProviderPaymentIntentResult>;

  updatePaymentIntent(
    params: UpdateProviderPaymentIntentParams,
  ): Promise<ProviderPaymentIntentResult>;

  getPaymentIntentByPaymentId(paymentIdAtProvider: string): Promise<ProviderPaymentIntentResult>;
}

export const PaymentProviderServicePort: unique symbol = Symbol('PAYMENT_PROVIDER_SERVICE');
