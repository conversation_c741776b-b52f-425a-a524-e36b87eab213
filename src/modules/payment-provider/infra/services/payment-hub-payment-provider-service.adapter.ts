import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';

import { PaymentType } from '@/modules/payment/domain/payment.entity';

import { PaymentHubConfig } from '../../../../infra/config/payment-hub.config';
import {
  CalculatePaymentProviderFeeParams,
  CreateProviderAccountParams,
  CreateProviderCustomerParams,
  CreateProviderFeeSettingsParams,
  CreateProviderPaymentIntentParams,
  CreateProviderRefundParams,
  PaymentHubGatewayType,
  PaymentProviderCurrency,
  PaymentProviderServicePort,
  ProviderAccountResult,
  ProviderCalculatedFee,
  ProviderCustomerResult,
  ProviderFeeSettingsResult,
  ProviderPaymentIntentCommonParams,
  ProviderPaymentIntentResult,
  ProviderRefundResult,
  UpdateProviderFeeSettingsParams,
  UpdateProviderPaymentIntentParams,
} from '../../domain/payment-provider-service.port';
import { PaymentProviderType } from '../../domain/payment-provider.entity';

interface CreateGatewayAccountParams {
  idAtGateway: string;
  accountId: string;
}

interface PaymentIntentCommonParams {
  amount: number;
  marketplaceOrderFee: number;
  platformOrderFee: number;
  currency: 'USD';
  paymentSplits: {
    accountId: string;
    amount: number;
  }[];
  paymentMethodType: 'card';
  feeSettingsId?: string | null;
  statementDescriptor?: string | null;
  metadata?: Record<string, unknown>;
}

interface CreatePaymentIntentParams extends PaymentIntentCommonParams {
  customerId: string;
  gatewayType: 'stripe';
}

interface UpdatePaymentIntentParams extends PaymentIntentCommonParams {}

interface GatewayAccount {
  id: string;
  idAtGateway: string;
}

interface Account {
  id: string;
  name: string | null;
  email: string | null;
}

interface Customer {
  id: string;
  name: string | null;
}

interface PaymentIntent {
  paymentIntentId: string;
  amount: number;
  currency: string;
  paymentMethodType: string;
  gatewayPaymentIntent: {
    stripePaymentIntentId: string;
  };
}

interface Refund {
  id: string;
  amount: number;
  currency: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

interface FeeSettings {
  id: string;
  marketplaceFeePercentage: number;
  marketplaceFeeFixed: number;
  isDefault: boolean;
}

enum PaymentHubCurrency {
  USD = 'USD',
}

@Injectable()
export class PaymentHubPaymentProviderServiceAdapter implements PaymentProviderServicePort {
  private axiosConfig = {
    baseURL: PaymentHubConfig.apiUrl,
    headers: {
      'Content-Type': 'application/json',
      'api-key': PaymentHubConfig.apiKey,
    },
  };

  private CARD_FEE_PERCENTAGE = 0.029; // 2.9%
  private CARD_FIXED_FEE = 30; // 0.3$

  constructor(private readonly httpService: HttpService) {}

  async createFeeSettings(
    params: CreateProviderFeeSettingsParams,
  ): Promise<ProviderFeeSettingsResult> {
    const feeSettings = await this.httpService.axiosRef
      .post<FeeSettings>(
        `/marketplace-gateways/${PaymentHubGatewayType.STRIPE}/fee-settings`,
        params,
        this.axiosConfig,
      )
      .then(({ data }) => this.mapFeeSettings(data));

    return feeSettings;
  }

  async updateFeeSettings(
    params: UpdateProviderFeeSettingsParams,
  ): Promise<ProviderFeeSettingsResult> {
    const feeSettings = await this.httpService.axiosRef
      .put<FeeSettings>(
        `/marketplace-gateways/${PaymentHubGatewayType.STRIPE}/fee-settings/${params.feeSettingsIdAtProvider}`,
        params,
        this.axiosConfig,
      )
      .then(({ data }) => this.mapFeeSettings(data));

    return feeSettings;
  }

  get currency(): PaymentProviderCurrency {
    return {
      USD: PaymentHubCurrency.USD,
    };
  }

  async createRefund(params: CreateProviderRefundParams): Promise<ProviderRefundResult> {
    const refund = await this.httpService.axiosRef
      .post<Refund>('/refunds', params, this.axiosConfig)
      .then(({ data }) => this.mapRefund(data));

    return refund;
  }

  async getPaymentIntentByPaymentId(
    paymentIdAtProvider: string,
  ): Promise<ProviderPaymentIntentResult> {
    return this.httpService.axiosRef
      .get<PaymentIntent>(`/payments/${paymentIdAtProvider}/payment-intent`, this.axiosConfig)
      .then(({ data }) => this.mapPaymentIntent(data));
  }

  get providerType() {
    return PaymentProviderType.PAYMENT_HUB;
  }

  get cardDefaultFee() {
    return {
      fixed: this.CARD_FIXED_FEE,
      percentage: this.CARD_FEE_PERCENTAGE,
    };
  }

  calculateFee(params: CalculatePaymentProviderFeeParams): ProviderCalculatedFee {
    if (params.paymentMethodType === PaymentType.CARD) {
      return this.calculateCardFee(params);
    }

    throw new Error(`Unsupported payment type ${params.paymentMethodType}`);
  }

  calculateCardFee({
    amount,
    isFeeIncluded,
    customFeeFixed = this.CARD_FIXED_FEE,
    customFeePercentage = this.CARD_FEE_PERCENTAGE,
  }: Pick<
    CalculatePaymentProviderFeeParams,
    'amount' | 'isFeeIncluded' | 'customFeeFixed' | 'customFeePercentage'
  >): ProviderCalculatedFee {
    const feeFixed = customFeeFixed || this.CARD_FIXED_FEE;
    const feePercentage = customFeePercentage || this.CARD_FEE_PERCENTAGE;

    const feeSettings = {
      feeFixed,
      feePercentage,
    };

    if (amount === 0) {
      return {
        ...feeSettings,
        fee: 0,
      };
    }

    if (isFeeIncluded) {
      return {
        ...feeSettings,
        fee: Math.round(amount * feePercentage + feeFixed),
      };
    }

    const amountWithSurcharge = (amount + feeFixed) / (1 - feePercentage);

    return {
      ...feeSettings,
      fee: Math.round(amountWithSurcharge - amount),
    };
  }

  async createCustomer(params: CreateProviderCustomerParams): Promise<ProviderCustomerResult> {
    const customer = await this.httpService.axiosRef
      .post<Customer>('/customers', params, this.axiosConfig)
      .then(({ data }) => this.mapCustomer(data));

    return customer;
  }

  async updatePaymentIntent({
    id,
    ...params
  }: UpdateProviderPaymentIntentParams): Promise<ProviderPaymentIntentResult> {
    const paymentIntent = await this.httpService.axiosRef
      .put<PaymentIntent>(
        `/payment-intents/${id}`,
        this.prepareUpdatePaymentIntentParams(params),
        this.axiosConfig,
      )
      .then(({ data }) => this.mapPaymentIntent(data));

    return paymentIntent;
  }

  async createPaymentIntent(
    params: CreateProviderPaymentIntentParams,
  ): Promise<ProviderPaymentIntentResult> {
    const paymentIntent = await this.httpService.axiosRef
      .post<PaymentIntent>(
        '/payment-intents',
        this.prepareCreatePaymentIntentParams(params),
        this.axiosConfig,
      )
      .then(({ data }) => this.mapPaymentIntent(data));

    return paymentIntent;
  }

  private preparePaymentIntentCommonParams(
    params: ProviderPaymentIntentCommonParams,
  ): PaymentIntentCommonParams {
    return {
      amount: params.amount,
      marketplaceOrderFee: params.marketplaceFee,
      platformOrderFee: 0,
      currency: 'USD',
      paymentSplits: params.paymentSplits,
      paymentMethodType: 'card',
      statementDescriptor: params.statementDescriptor,
      metadata: params.metadata,
    };
  }

  private prepareCreatePaymentIntentParams(
    params: CreateProviderPaymentIntentParams,
  ): CreatePaymentIntentParams {
    return {
      ...this.preparePaymentIntentCommonParams(params),
      customerId: params.customerId,
      gatewayType: 'stripe',
      feeSettingsId: params.feeSettingsIdAtProvider,
    };
  }

  private prepareUpdatePaymentIntentParams(
    params: Omit<UpdateProviderPaymentIntentParams, 'id'>,
  ): UpdatePaymentIntentParams {
    return this.preparePaymentIntentCommonParams(params);
  }

  async createAccount({
    idAtProvider,
    ...params
  }: CreateProviderAccountParams): Promise<ProviderAccountResult> {
    const account = await this.httpService.axiosRef
      .post<Account>('/accounts', params, this.axiosConfig)
      .then(({ data }) => this.mapAccount(data));

    await this.createGatewayAccount({
      idAtGateway: idAtProvider,
      accountId: account.id,
    });

    return account;
  }

  private async createGatewayAccount({
    accountId,
    ...params
  }: CreateGatewayAccountParams): Promise<GatewayAccount> {
    return this.httpService.axiosRef
      .post<GatewayAccount>(
        `/accounts/${accountId}/gateway-accounts`,
        {
          ...params,
          gatewayType: 'stripe',
        },
        this.axiosConfig,
      )
      .then(({ data }) => data);
  }

  private mapFeeSettings(feeSettings: FeeSettings): ProviderFeeSettingsResult {
    return {
      id: feeSettings.id,
      marketplaceFeeFixed: feeSettings.marketplaceFeeFixed,
      marketplaceFeePercentage: feeSettings.marketplaceFeePercentage,
      isDefault: feeSettings.isDefault,
      providerType: PaymentProviderType.PAYMENT_HUB,
    };
  }

  private mapRefund(refund: Refund): ProviderRefundResult {
    return {
      id: refund.id,
      amount: refund.amount,
      providerType: PaymentProviderType.PAYMENT_HUB,
      currency: refund.currency,
    };
  }

  private mapPaymentIntent(paymentIntent: PaymentIntent): ProviderPaymentIntentResult {
    return {
      id: paymentIntent.paymentIntentId,
      amount: paymentIntent.amount,
      providerType: PaymentProviderType.PAYMENT_HUB,
      currency: paymentIntent.currency,
      paymentMethodType: paymentIntent.paymentMethodType,
      paymentIntentIdAtGateway: paymentIntent.gatewayPaymentIntent.stripePaymentIntentId,
    };
  }

  private mapAccount(account: Account): ProviderAccountResult {
    return {
      id: account.id,
      email: account.email,
      name: account.name,
      providerType: PaymentProviderType.PAYMENT_HUB,
    };
  }

  private mapCustomer(customer: Customer): ProviderCustomerResult {
    return {
      id: customer.id,
      name: customer.name,
      providerType: PaymentProviderType.PAYMENT_HUB,
    };
  }

  // eslint-disable-next-line @typescript-eslint/require-await, @typescript-eslint/no-unused-vars
  async updateAccount(params: CreateProviderAccountParams): Promise<ProviderAccountResult> {
    throw new Error('Not implemented');
  }
}
