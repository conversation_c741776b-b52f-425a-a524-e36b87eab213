import { Inject, Injectable } from '@nestjs/common';
import { UseCase } from '@sw-web/nestjs-core/domain';

import { PointOfSaleRepositoryPort } from '@/modules/point-of-sale/domain/point-of-sale-repository.port';
import { PointOfSaleNotFoundError } from '@/modules/point-of-sale/errors';

import { BanApplicationService } from '../../services/ban.application-service';

type Props = {
  pointOfSaleId: string;
  fingerprint: string | null;
  email: string | null;
};

@Injectable()
export class ValidateFingerprintAndEmailByPosIdUseCase implements UseCase<Props, void> {
  constructor(
    @Inject(PointOfSaleRepositoryPort)
    private readonly pointOfSaleRepository: PointOfSaleRepositoryPort,
    private readonly banApplicationService: BanApplicationService,
  ) {}

  async execute(props: Props): Promise<void> {
    const pointOfSale = await this.pointOfSaleRepository.findById(props.pointOfSaleId);

    if (!pointOfSale) {
      throw new PointOfSaleNotFoundError();
    }

    if (!props.fingerprint && !props.email) {
      throw new Error('At least one of fingerprint or email must be provided');
    }

    if (props.fingerprint) {
      await this.banApplicationService.validateFingerprint(
        props.fingerprint,
        pointOfSale.getProps().marketplaceId,
      );
    }

    if (props.email) {
      await this.banApplicationService.validateEmail(
        props.email,
        pointOfSale.getProps().marketplaceId,
      );
    }
  }
}
