import { Body, Controller, Param, Post } from '@nestjs/common';

import { BanEmailDto } from './dto/ban-email.dto';
import { BanFingerprintDto } from './dto/ban-fingerprint.dto';
import { ValidateFingerprintAndEmailDto } from './dto/validate-fingerprint-and-email.dto';
import { BanApplicationService } from '../application/services/ban.application-service';
import { ValidateFingerprintAndEmailByPosIdUseCase } from '../application/use-cases/validate-fingerprint-and-email-by-pos-id/validate-fingerprint-by-pos-id.use-case';

@Controller()
export class BanController {
  constructor(
    private readonly banApplicationService: BanApplicationService,
    private readonly validateFingerprintAndEmailByPosIdUseCase: ValidateFingerprintAndEmailByPosIdUseCase,
  ) {}

  @Post('point-of-sales/:pointOfSaleId/bans/check')
  async checkBan(
    @Body() dto: ValidateFingerprintAndEmailDto,
    @Param('pointOfSaleId') pointOfSaleId: string,
  ) {
    await this.validateFingerprintAndEmailByPosIdUseCase.execute({
      fingerprint: dto.fingerprint || null,
      email: dto.email || null,
      pointOfSaleId,
    });

    return {
      success: true,
    };
  }

  @Post('bans/emails')
  async banEmail(@Body() dto: BanEmailDto) {
    await this.banApplicationService.banEmail(dto);
  }

  @Post('bans/fingerprints')
  async banFingerprint(@Body() dto: BanFingerprintDto) {
    await this.banApplicationService.banFingerprint(dto);
  }
}
