import { Inject } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@sw-web/nestjs-core/event-bus';

import { OrderCanceledEvent } from '@/modules/order/domain/events/order-canceled.event';
import { OrderCreatedEvent } from '@/modules/order/domain/events/order-created.event';
import { OrderPaidEvent } from '@/modules/order/domain/events/order-paid.event';
import { OrderUpdatedEvent } from '@/modules/order/domain/events/order-updated.event';
import { PaymentRepositoryPort } from '@/modules/payment/domain/payment-repository.port';
import { PaymentEntity } from '@/modules/payment/domain/payment.entity';
import { PaymentMethodRepositoryPort } from '@/modules/payment-method/domain/payment-method-repository.port';
import { PaymentProviderServicePort } from '@/modules/payment-provider/domain/payment-provider-service.port';

import { WebhookEventObject, WebhookEventType } from '../../domain/webhook-event.entity';
import { OrderWebhookEventDataMapper } from '../mappers/webhook-event-data/order-webhook-data.mapper';
import { WebhookEventApplicationService } from '../service/webhook-event.application-service';

const EVENTS = [OrderPaidEvent, OrderCreatedEvent, OrderUpdatedEvent, OrderCanceledEvent] as const;

type OrderEvent = InstanceType<(typeof EVENTS)[number]>;

@EventsHandler(...EVENTS)
export class CreateOrderWebhookEventsEventHandler implements IEventHandler<OrderEvent> {
  constructor(
    @Inject(PaymentRepositoryPort)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PaymentMethodRepositoryPort)
    private readonly paymentMethodRepository: PaymentMethodRepositoryPort,
    private readonly orderWebhookDataMapper: OrderWebhookEventDataMapper,
    private readonly webhookEventApplicationService: WebhookEventApplicationService,
    @Inject(PaymentProviderServicePort)
    private readonly paymentProviderService: PaymentProviderServicePort,
  ) {}

  async handle(event: OrderEvent) {
    const { order } = event.getProps();

    const payments = await this.paymentRepository.findByOrderId(order.getId());

    const paymentMethods = await this.findPaymentMethods(payments);

    const paymentIntentsAtProvider = await this.findPaymentIntentsAtProvider(payments);

    const webhookData = this.orderWebhookDataMapper.toUi({
      order,
      payments,
      paymentMethods,
      paymentIntentsAtProvider,
    });

    return this.webhookEventApplicationService.createWebhookEvents({
      pointOfSaleId: order.getProps().pointOfSaleId,
      data: webhookData,
      type: this.mapEventToWebhookType(event),
      object: WebhookEventObject.ORDER,
      objectId: order.getId(),
    });
  }

  private async findPaymentIntentsAtProvider(payments: PaymentEntity[]) {
    const promises = payments.map(async (payment) => {
      const { paymentIdAtProvider } = payment.getProps();
      const paymentIntentAtProvider =
        await this.paymentProviderService.getPaymentIntentByPaymentId(paymentIdAtProvider);
      return { paymentIdAtProvider, paymentIntentAtProvider };
    });

    return Promise.all(promises);
  }

  private async findPaymentMethods(payments: PaymentEntity[]) {
    const uniquePaymentMethodIds = new Set<string>();

    payments.forEach((x) => {
      const { paymentMethodId } = x.getProps();

      if (paymentMethodId) {
        uniquePaymentMethodIds.add(paymentMethodId);
      }
    });

    if (!uniquePaymentMethodIds.size) {
      return [];
    }

    return this.paymentMethodRepository.findByIds(Array.from(uniquePaymentMethodIds));
  }

  private mapEventToWebhookType(event: OrderEvent) {
    if (event instanceof OrderCreatedEvent) {
      return WebhookEventType.ORDER_CREATED;
    }

    if (event instanceof OrderUpdatedEvent) {
      return WebhookEventType.ORDER_UPDATED;
    }

    if (event instanceof OrderCanceledEvent) {
      return WebhookEventType.ORDER_CANCELED;
    }

    if (event instanceof OrderPaidEvent) {
      return WebhookEventType.ORDER_PAID;
    }

    throw new Error('Invalid event');
  }
}
