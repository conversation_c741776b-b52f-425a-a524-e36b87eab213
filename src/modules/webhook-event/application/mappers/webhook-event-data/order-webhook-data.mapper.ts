import { keyBy } from 'lodash';

import { OrderCustomFieldEntity } from '@/modules/order/domain/order-custom-field.entity';
import { OrderItemHolderEntity } from '@/modules/order/domain/order-item-holder.entity';
import { OrderItemEntity } from '@/modules/order/domain/order-item.entity';
import { OrderEntity } from '@/modules/order/domain/order.entity';
import { OrderItemHolderDto } from '@/modules/order/presentation/dto/order.dto';
import { PaymentEntity } from '@/modules/payment/domain/payment.entity';
import { PaymentMethodEntity } from '@/modules/payment-method/domain/payment-method.entity';
import { ProviderPaymentIntentResult } from '@/modules/payment-provider/domain/payment-provider-service.port';
import { OrderWebhookEventDto } from '@/modules/webhook-event/presentation/dto/order-webhook-event.dto';

type Props = {
  paymentIntentsAtProvider: {
    paymentIdAtProvider: string;
    paymentIntentAtProvider: ProviderPaymentIntentResult;
  }[];
  order: OrderEntity;
  payments: PaymentEntity[];
  paymentMethods: PaymentMethodEntity[];
};

export class OrderWebhookEventDataMapper {
  toUi({
    order,
    paymentIntentsAtProvider,
    payments,
    paymentMethods,
  }: Props): OrderWebhookEventDto['data'] {
    const props = order.getProps();

    const paymentMethodsById = keyBy(paymentMethods, (x) => x.getProps().id);

    return {
      id: props.id,
      pointOfSaleId: props.pointOfSaleId,
      amount: props.currentAmount,
      customerId: props.customerId,
      status: props.status,
      orderItems: props.orderItems.map((x) => this.mapOrderItemUi(x)),
      customFields: props.customFields.map((x) => this.mapCustomFieldUi(x)),
      customer: this.mapCustomerInfo(order),
      recipient: {
        email: props.recipientInfo.email,
      },
      entryCode: props.entryCode,
      payments: payments.map((payment) =>
        this.mapPaymentUi(
          payment,
          payment.getProps().paymentMethodId
            ? paymentMethodsById[payment.getProps().paymentMethodId as string]
            : null,
          paymentIntentsAtProvider.find(
            (x) => x.paymentIdAtProvider === payment.getProps().paymentIdAtProvider,
          )?.paymentIntentAtProvider || null,
        ),
      ),
    };
  }

  private mapPaymentUi(
    payment: PaymentEntity,
    paymentMethod: PaymentMethodEntity | null,
    paymentIntentAtProvider: ProviderPaymentIntentResult | null,
  ): OrderWebhookEventDto['data']['payments'][number] {
    const props = payment.getProps();

    return {
      id: props.id,
      orderId: props.orderId,
      initialAmount: props.initialAmount,
      currentAmount: props.currentAmount,
      paymentIdAtProvider: props.paymentIdAtProvider,
      paymentProviderType: props.paymentProviderType,
      type: props.type,
      status: props.status,
      totalFeeAmount: props.totalFeeAmount,
      netAmount: props.netAmount,
      paymentProviderFee: props.currentPaymentProviderFee,
      marketplaceFee: props.currentMarketplaceFee,
      paymentProviderFeePayer: props.paymentProviderFeePayer,
      marketplaceFeePayer: props.marketplaceFeePayer,
      clientInfo: props.clientInfo,
      paymentIntentIdAtGateway: paymentIntentAtProvider?.paymentIntentIdAtGateway || null,
      paymentMethod: paymentMethod ? this.mapPaymentMethodUi(paymentMethod) : null,
    };
  }

  private mapPaymentMethodUi(
    paymentMethod: PaymentMethodEntity,
  ): OrderWebhookEventDto['data']['payments'][number]['paymentMethod'] {
    const props = paymentMethod.getProps();

    return {
      id: props.id,
      fingerprintAtProvider: props.fingerprintAtProvider,
      cardExpiryMonth: props.cardExpiryMonth,
      cardExpiryYear: props.cardExpiryYear,
      cardLast4: props.cardLast4,
    };
  }

  private mapCustomerInfo(order: OrderEntity): OrderWebhookEventDto['data']['customer'] {
    const { customerInfo } = order.getProps();

    return {
      firstName: customerInfo.firstName,
      lastName: customerInfo.lastName,
      zip: customerInfo.zip,
      phone: customerInfo.phone,
      email: customerInfo.email,
      country: customerInfo.country,
    };
  }

  private mapOrderItemUi(
    orderItem: OrderItemEntity,
  ): OrderWebhookEventDto['data']['orderItems'][number] {
    const props = orderItem.getProps();

    return {
      id: props.id,
      totalAmount: props.currentTotalAmount,
      totalMarketplaceFee: props.currentTotalMarketplaceFee,
      price: props.price,
      quantity: props.currentQuantity,
      productId: props.productId,
      marketplaceFee: props.marketplaceFee,
      holder: props.holder && this.mapOrderItemHolderUi(props.holder),
    };
  }

  private mapOrderItemHolderUi(holder: OrderItemHolderEntity): OrderItemHolderDto {
    const holderProps = holder.getProps();

    return {
      firstName: holderProps.firstName,
      lastName: holderProps.lastName,
      address: holderProps.address,
      zip: holderProps.zip,
      phone: holderProps.phone,
      email: holderProps.email,
      country: holderProps.country,
      state: holderProps.state,
    };
  }

  private mapCustomFieldUi(
    customField: OrderCustomFieldEntity,
  ): OrderWebhookEventDto['data']['customFields'][number] {
    const props = customField.getProps();

    return {
      customFieldId: props.customFieldId,
      value: props.value,
    };
  }
}
