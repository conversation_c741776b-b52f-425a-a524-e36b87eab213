import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNumber,
  IsEnum,
  ArrayMinSize,
  ValidateNested,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsObject,
  IsEmail,
  IsPhoneNumber,
} from 'class-validator';

import { IsUlid } from '@/core/infra';
import { FeePayer } from '@/modules/fee/domain/fee-payer.enum';
import { OrderStatus } from '@/modules/order/domain/order.entity';
import { OrderItemHolderDto } from '@/modules/order/presentation/dto/order.dto';
import { PaymentStatus, PaymentType } from '@/modules/payment/domain/payment.entity';
import { PaymentProviderType } from '@/modules/payment-provider/domain/payment-provider.entity';
import { ClientInfo } from '@/modules/shared/interfaces/client-info.interface';

import { WebhookEventDto } from './webhook-event.dto';

export class OrderWebhookEventDto extends WebhookEventDto {
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => OrderWebhookData)
  data: OrderWebhookData;
}

class OrderPaymentDto {
  @ApiProperty({ description: 'ID of the payment' })
  @IsUlid()
  id: string;

  @ApiProperty({ description: 'ID of the order' })
  @IsUlid()
  orderId: string | null;

  @ApiProperty({ description: 'ID of provider payment' })
  @IsUlid()
  paymentIdAtProvider: string;

  @ApiProperty({ description: 'Payment initial amount' })
  @IsNumber()
  initialAmount: number;

  @ApiProperty({ description: 'Payment current amount' })
  @IsNumber()
  currentAmount: number;

  @ApiProperty({ description: 'Payment status' })
  @IsEnum(PaymentStatus)
  status: PaymentStatus;

  @ApiProperty({ description: 'Payment type' })
  @IsEnum(PaymentType)
  type: PaymentType;

  @ApiProperty({ description: 'Payment provider type' })
  @IsEnum(PaymentProviderType)
  paymentProviderType: PaymentProviderType;

  @ApiProperty({ description: 'Payment total fee amount' })
  @IsNumber()
  totalFeeAmount: number;

  @ApiProperty({ description: 'Payment net amount' })
  @IsNumber()
  netAmount: number;

  @ApiProperty({ description: 'Payment provider fee' })
  @IsNumber()
  paymentProviderFee: number;

  @ApiProperty({ description: 'Payment marketplace fee' })
  @IsNumber()
  marketplaceFee: number;

  @ApiProperty({ description: 'Payment provider fee payer' })
  @IsEnum(FeePayer)
  paymentProviderFeePayer: FeePayer;

  @ApiProperty({ description: 'Payment marketplace fee payer' })
  @IsEnum(FeePayer)
  marketplaceFeePayer: FeePayer;

  @IsObject()
  clientInfo: ClientInfo;

  @IsObject()
  @IsOptional()
  paymentMethod: PaymentMethodData | null;

  @IsString()
  @IsOptional()
  paymentIntentIdAtGateway: string | null;
}

class PaymentMethodData {
  @ApiProperty({ description: 'ID of the payment method' })
  @IsUlid()
  id: string;

  @ApiProperty({ description: 'Fingerprint at provider' })
  @IsString()
  @IsOptional()
  fingerprintAtProvider: string | null;

  @ApiProperty({ description: 'Card last 4' })
  @IsString()
  @IsOptional()
  cardLast4: string | null;

  @ApiProperty({ description: 'Card expiry year' })
  @IsNumber()
  @IsOptional()
  cardExpiryYear: number | null;

  @ApiProperty({ description: 'Card expiry month' })
  @IsNumber()
  @IsOptional()
  cardExpiryMonth: number | null;
}

class CustomerDto {
  @ApiProperty({ description: 'Customer first name', nullable: true })
  @IsString()
  @IsOptional()
  firstName: string | null;

  @ApiProperty({ description: 'Customer last name', nullable: true })
  @IsString()
  @IsOptional()
  lastName: string | null;

  @ApiProperty({ description: 'Customer zip', nullable: true })
  @IsString()
  @IsOptional()
  zip: string | null;

  @ApiProperty({ description: 'Customer phone', nullable: true })
  @IsPhoneNumber()
  @IsOptional()
  phone: string | null;

  @ApiProperty({ description: 'Customer email', nullable: true })
  @IsEmail()
  @IsOptional()
  email: string | null;

  @ApiProperty({ description: 'Customer country', nullable: true })
  @IsString()
  @IsOptional()
  country: string | null;
}

class OrderWebhookData {
  @ApiProperty({ description: 'ID of the order' })
  @IsUlid()
  id: string;

  @ApiProperty({ description: 'ID of point of sale' })
  @IsUlid()
  pointOfSaleId: string | null;

  @ApiProperty({ description: 'ID of customer' })
  @IsUlid()
  customerId: string;

  @ApiProperty({ description: 'Order amount' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Entry code' })
  @IsString()
  @IsOptional()
  entryCode: string | null;

  @ApiProperty({ description: 'Order status' })
  @IsEnum(OrderStatus)
  status: OrderStatus;

  @ArrayMinSize(1)
  @ValidateNested()
  @Type(() => OrderItemDto)
  orderItems: OrderItemDto[];

  @ValidateNested()
  @Type(() => OrderCustomFieldDto)
  customFields: OrderCustomFieldDto[];

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CustomerDto)
  customer: CustomerDto;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => RecipientDto)
  recipient: RecipientDto;

  @ValidateNested()
  @Type(() => OrderPaymentDto)
  payments: OrderPaymentDto[];
}

class RecipientDto {
  @ApiProperty({ description: 'Customer email' })
  @IsEmail()
  @IsNotEmpty()
  email?: string | null;
}

class OrderItemDto {
  @ApiProperty({ description: 'ID of the order item' })
  @IsUlid()
  id: string;

  @ApiProperty({ description: 'Order item total amount' })
  @IsNumber()
  totalAmount: number;

  @ApiProperty({ description: 'Order item total marketplace fee' })
  @IsNumber()
  totalMarketplaceFee: number;

  @ApiProperty({ description: 'Order item price' })
  @IsNumber()
  price: number;

  @ApiProperty({ description: 'Order item quantity' })
  @IsNumber()
  quantity: number;

  @ApiProperty({ description: 'ID of product' })
  @IsUlid()
  productId: string;

  @ApiProperty({ description: 'Marketplace Fee' })
  @IsNumber()
  marketplaceFee: number;

  @IsOptional()
  @ValidateNested()
  @Type(() => OrderItemDto)
  holder: OrderItemHolderDto | null;
}

class OrderCustomFieldDto {
  @ApiProperty({ description: 'Custom Field Id' })
  @IsUlid()
  customFieldId: string;

  @ApiProperty({ description: 'Custom field response value' })
  @IsNotEmpty()
  @IsString()
  value: string;
}
