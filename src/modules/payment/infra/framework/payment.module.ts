import { Module } from '@nestjs/common';

import { BanModule } from '@/modules/ban/infra/framework/ban.module';
import { FeeModule } from '@/modules/fee/infra/framework/fee.module';
import { MarketplaceModule } from '@/modules/marketplace/infra/framework/marketplace.module';
import { OrderModule } from '@/modules/order/infra/framework/order.module';
import { PointOfSaleModule } from '@/modules/point-of-sale/infra/framework/point-of-sale.module';

import {
  PAYMENT_APPLICATION_SERVICES,
  PAYMENT_MAPPERS,
  PAYMENT_USE_CASES,
} from '../../application';
import { PaymentRepositoryPort } from '../../domain/payment-repository.port';
import { PaymentController } from '../../presentation/payment.controller';
import { PrismaPaymentRepositoryAdapter } from '../repositories/prisma-payment-repository.adapter';

const REPOSITORIES = [
  {
    provide: PaymentRepositoryPort,
    useClass: PrismaPaymentRepositoryAdapter,
  },
];

@Module({
  imports: [MarketplaceModule, PointOfSaleModule, BanModule, OrderModule, FeeModule],
  controllers: [PaymentController],
  providers: [
    ...REPOSITORIES,
    ...PAYMENT_USE_CASES,
    ...PAYMENT_APPLICATION_SERVICES,
    ...PAYMENT_MAPPERS,
  ],
  exports: [...REPOSITORIES, ...PAYMENT_APPLICATION_SERVICES],
})
export class PaymentModule {}
