/* eslint-disable @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { Paginated, PaginatedQueryParams, RepositoryBase } from '@sw-web/nestjs-core/domain';

import { PrismaService } from '@/infra/drivers/prisma/prisma.service';

import { PaymentMapper } from '../../application/mappers/payment.mapper';
import { PaymentRepositoryPort } from '../../domain/payment-repository.port';
import { PaymentEntity } from '../../domain/payment.entity';

const PaymentPrismaValidator = Prisma.validator<Prisma.PaymentDefaultArgs>()({});

@Injectable()
export class PrismaPaymentRepositoryAdapter
  extends RepositoryBase<PaymentEntity>
  implements PaymentRepositoryPort
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly mapper: PaymentMapper,
  ) {
    super(prisma);
  }

  async findLatestByFingerprintAtProvider(
    fingerprintAtProvider: string,
  ): Promise<PaymentEntity | null> {
    const payment = await this.prisma.payment.findFirst({
      ...PaymentPrismaValidator,
      where: {
        paymentMethod: {
          fingerprintAtProvider,
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return payment && this.mapper.toDomain(payment);
  }

  async findByOrderId(orderId: string): Promise<PaymentEntity[]> {
    const payments = await this.prisma.payment.findMany({
      ...PaymentPrismaValidator,
      where: {
        orderId,
      },
    });

    return payments.map((x) => this.mapper.toDomain(x));
  }

  async findByProviderPaymentId(providerPaymentId: string): Promise<PaymentEntity | null> {
    const payment = await this.prisma.payment.findFirst({
      ...PaymentPrismaValidator,
      where: {
        paymentIdAtProvider: providerPaymentId,
      },
    });

    return payment && this.mapper.toDomain(payment);
  }

  async findBySessionId(paymentSessionId: string): Promise<PaymentEntity | null> {
    const payment = await this.prisma.payment.findFirst({
      ...PaymentPrismaValidator,
      where: {
        paymentSessionId,
      },
    });

    return payment && this.mapper.toDomain(payment);
  }

  async create(order: PaymentEntity): Promise<void> {
    await this.prisma.payment.create({
      data: this.mapper.toPersistence(order) as Prisma.PaymentUncheckedCreateInput,
    });
  }

  async update(payment: PaymentEntity): Promise<void> {
    await this.prisma.payment.update({
      data: this.mapper.toPersistence(payment) as Prisma.PaymentUncheckedCreateInput,
      where: {
        paymentId: payment.getId(),
      },
    });
  }

  async delete(entity: PaymentEntity): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  async findById(id: string): Promise<PaymentEntity | null> {
    const payment = await this.prisma.payment.findFirst({
      ...PaymentPrismaValidator,
      where: {
        paymentId: id,
      },
    });

    return payment && this.mapper.toDomain(payment);
  }

  async findAll(): Promise<PaymentEntity[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<PaymentEntity>> {
    throw new Error('Method not implemented.');
  }
}
