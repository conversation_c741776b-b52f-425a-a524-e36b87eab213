import { IRepository } from '@sw-web/nestjs-core/domain';

import { PaymentEntity } from './payment.entity';

export interface PaymentRepositoryPort extends IRepository<PaymentEntity> {
  findBySessionId(paymentSessionId: string): Promise<PaymentEntity | null>;

  findByProviderPaymentId(providerPaymentId: string): Promise<PaymentEntity | null>;

  findByOrderId(orderId: string): Promise<PaymentEntity[]>;

  findLatestByFingerprintAtProvider(fingerprintAtProvider: string): Promise<PaymentEntity | null>;
}

export const PaymentRepositoryPort: unique symbol = Symbol('PAYMENT_REPOSITORY');
