import { DomainError, IBusinessRule } from '@sw-web/nestjs-core/domain';
import dayjs from 'dayjs';

import { PaymentCardCoolDownError } from '../../errors';
import { PaymentEntity } from '../payment.entity';

export class DuplicateCardCoolDownRule implements IBusinessRule {
  private error: DomainError | null;

  private CARD_COOL_DOWN_MINUTES = 3;

  constructor(private readonly lastPaymentWithSameCard: PaymentEntity) {}

  getError(): DomainError {
    return this.error!;
  }

  isBroken(): boolean {
    const lastPaymentDate = dayjs(this.lastPaymentWithSameCard.getProps().createdAt);
    const currentDate = dayjs();

    const minutesSinceLastPayment = currentDate.diff(lastPaymentDate, 'minute');

    if (minutesSinceLastPayment < this.CARD_COOL_DOWN_MINUTES) {
      this.error = new PaymentCardCoolDownError(minutesSinceLastPayment);
      return true;
    }

    return false;
  }
}
