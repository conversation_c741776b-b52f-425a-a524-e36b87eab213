import { Inject, Injectable } from '@nestjs/common';
import { checkRules, UseCase } from '@sw-web/nestjs-core/domain';

import { BanApplicationService } from '@/modules/ban/application/services/ban.application-service';
import { PaymentRepositoryPort } from '@/modules/payment/domain/payment-repository.port';
import { DuplicateCardCoolDownRule } from '@/modules/payment/domain/rules/duplicate-card-cool-down.rule';
import { PointOfSaleRepositoryPort } from '@/modules/point-of-sale/domain/point-of-sale-repository.port';
import { PointOfSaleNotFoundError } from '@/modules/point-of-sale/errors';

interface Props {
  pointOfSaleId: string;
  fingerprintAtProvider: string;
  skipCardCoolDownCheck?: boolean;
}

@Injectable()
export class ValidatePaymentCardUseCase implements UseCase<Props, { isValid: boolean }> {
  constructor(
    @Inject(PaymentRepositoryPort)
    private readonly paymentRepository: PaymentRepositoryPort,
    @Inject(PointOfSaleRepositoryPort)
    private readonly pointOfSaleRepository: PointOfSaleRepositoryPort,
    private readonly banApplicationService: BanApplicationService,
  ) {}

  async execute(props: Props) {
    const payment = await this.paymentRepository.findLatestByFingerprintAtProvider(
      props.fingerprintAtProvider,
    );

    const pointOfSale = await this.pointOfSaleRepository.findById(props.pointOfSaleId);

    if (!pointOfSale) {
      throw new PointOfSaleNotFoundError();
    }

    await this.banApplicationService.validateFingerprint(
      props.fingerprintAtProvider,
      pointOfSale.getProps().marketplaceId,
    );

    if (payment && !props.skipCardCoolDownCheck) {
      checkRules(new DuplicateCardCoolDownRule(payment));
    }

    return {
      isValid: true,
    };
  }
}
