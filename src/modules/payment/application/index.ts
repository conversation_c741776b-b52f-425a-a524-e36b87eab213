import { PaymentMapper } from './mappers/payment.mapper';
import { PaymentApplicationService } from './service/payment.application-service';
import { GetPaymentUseCase } from './use-cases/get-payment/get-payment.use-case';
import { GetPaymentBySessionIdUseCase } from './use-cases/get-payment-by-session-id/get-payment-by-session-id.use-case';
import { ValidatePaymentCardUseCase } from './use-cases/validate-payment-card/validate-payment-card.use-case';

export const PAYMENT_USE_CASES = [
  GetPaymentUseCase,
  GetPaymentBySessionIdUseCase,
  ValidatePaymentCardUseCase,
];

export const PAYMENT_APPLICATION_SERVICES = [PaymentApplicationService];

export const PAYMENT_MAPPERS = [PaymentMapper];
