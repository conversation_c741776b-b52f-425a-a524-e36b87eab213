import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';

import { MarketplaceApiKeyGuard } from '@/infra/guards';

import { ValidatePaymentCardDto } from './dto/validate-payment-card.dto';
import { GetPaymentUseCase } from '../application/use-cases/get-payment/get-payment.use-case';
import { GetPaymentBySessionIdUseCase } from '../application/use-cases/get-payment-by-session-id/get-payment-by-session-id.use-case';
import { ValidatePaymentCardUseCase } from '../application/use-cases/validate-payment-card/validate-payment-card.use-case';

@Controller()
export class PaymentController {
  constructor(
    private readonly getPayment: GetPaymentUseCase,
    private readonly getPaymentBySessionId: GetPaymentBySessionIdUseCase,
    private readonly validatePaymentCard: ValidatePaymentCardUseCase,
  ) {}

  @Post('point-of-sales/:pointOfSaleId/payments/validate-card')
  public async validateCard(
    @Param('pointOfSaleId') posId: string,
    @Body() dto: ValidatePaymentCardDto,
  ) {
    return this.validatePaymentCard.execute({
      pointOfSaleId: posId,
      fingerprintAtProvider: dto.fingerprintAtProvider,
      skipCardCoolDownCheck: dto.skipCardCoolDownCheck,
    });
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Get('payments/:paymentId')
  public async get(@Param('paymentId') paymentId: string) {
    return this.getPayment.execute({
      paymentId,
    });
  }

  @Get('payments/session/:paymentSessionId')
  public async getByPaymentSession(@Param('paymentSessionId') paymentSessionId: string) {
    return this.getPaymentBySessionId.execute({
      paymentSessionId,
    });
  }
}
