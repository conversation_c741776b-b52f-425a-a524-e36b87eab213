import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';

import { RequestMarketplace } from '@/application/interfaces/request-marketplace.interface';
import { RequestMarketplaceCtx } from '@/infra/decorators';
import { MarketplaceApiKeyGuard } from '@/infra/guards';

import { CreatePointOfSaleDto } from './dto/create-point-of-sale.dto';
import { UpdatePointOfSaleDto } from './dto/update-point-of-sale.dto';
import { ValidateEntryCodeDto } from './dto/validate-entry-code.dto';
import { CreatePointOfSaleUseCase } from '../application/use-cases/create-point-of-sale/create-point-of-sale.use-case';
import { GetPointOfSaleUseCase } from '../application/use-cases/get-point-of-sale/get-point-of-sale.use-case';
import { UpdatePointOfSaleUseCase } from '../application/use-cases/update-point-of-sale/update-point-of-sale.use-case';
import { ValidateEntryCodeUseCase } from '../application/use-cases/validate-entry-code/validate-entry-code.use-case';

@Controller('point-of-sales')
export class PointOfSaleController {
  constructor(
    private readonly createPointOfSale: CreatePointOfSaleUseCase,
    private readonly updatePointOfSale: UpdatePointOfSaleUseCase,
    private readonly getPointOfSale: GetPointOfSaleUseCase,
    private readonly validateEntryCodeUseCase: ValidateEntryCodeUseCase,
  ) {}

  @UseGuards(MarketplaceApiKeyGuard)
  @Post()
  public async create(
    @Body() dto: CreatePointOfSaleDto,
    @RequestMarketplaceCtx() marketplace: RequestMarketplace,
  ) {
    return this.createPointOfSale.execute({
      ...dto,
      marketplaceId: marketplace.id,
      dateEnd: dto.dateEnd ? new Date(dto.dateEnd) : null,
      dateStart: dto.dateStart ? new Date(dto.dateStart) : null,
      salesStartDate: dto.salesStartDate ? new Date(dto.salesStartDate) : null,
      salesEndDate: dto.salesEndDate ? new Date(dto.salesEndDate) : null,
    });
  }

  @Post(':pointOfSaleId/validate-entry-code')
  public async validateEntryCode(
    @Body() dto: ValidateEntryCodeDto,
    @Param('pointOfSaleId') pointOfSaleId: string,
  ) {
    return this.validateEntryCodeUseCase.execute({
      pointOfSaleId,
      entryCode: dto.entryCode,
    });
  }

  // TODO: ADD ACCESS CONTROL
  @UseGuards(MarketplaceApiKeyGuard)
  @Put(':pointOfSaleId')
  public async update(
    @Body() dto: UpdatePointOfSaleDto,
    @Param('pointOfSaleId') pointOfSaleId: string,
  ) {
    return this.updatePointOfSale.execute({
      ...dto,
      pointOfSaleId,
      dateEnd: dto.dateEnd ? new Date(dto.dateEnd) : null,
      dateStart: dto.dateStart ? new Date(dto.dateStart) : null,
      salesStartDate: dto.salesStartDate ? new Date(dto.salesStartDate) : null,
      salesEndDate: dto.salesEndDate ? new Date(dto.salesEndDate) : null,
    });
  }

  @Get(':pointOfSaleId')
  public async get(@Param('pointOfSaleId') pointOfSaleId: string) {
    return this.getPointOfSale.execute({
      pointOfSaleId,
    });
  }
}
