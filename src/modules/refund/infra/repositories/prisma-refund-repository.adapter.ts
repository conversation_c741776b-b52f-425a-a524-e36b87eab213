/* eslint-disable @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unused-vars */

import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { PaginatedQueryParams, Paginated, RepositoryBase } from '@sw-web/nestjs-core/domain';

import { PrismaService } from '@/infra/drivers/prisma/prisma.service';

import { RefundItemEntity } from '../../domain/refund-item.entity';
import { RefundRepositoryPort } from '../../domain/refund-repository.port';
import { RefundEntity, RefundStatus } from '../../domain/refund.entity';

const RefundPrismaValidator = Prisma.validator<Prisma.RefundDefaultArgs>()({
  include: {
    refundItems: true,
  },
});
export type RefundDbRecord = Prisma.RefundGetPayload<typeof RefundPrismaValidator>;

@Injectable()
export class RefundRepositoryAdapter
  extends RepositoryBase<RefundEntity>
  implements RefundRepositoryPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma);
  }

  async findByProviderId(refundIdAtProvider: string): Promise<RefundEntity | null> {
    const refund = await this.prisma.refund.findFirst({
      ...RefundPrismaValidator,
      where: { idAtProvider: refundIdAtProvider },
    });

    return refund && this.mapToEntity(refund);
  }

  async findPendingByPaymentId(paymentId: string): Promise<RefundEntity[]> {
    const refunds = await this.prisma.refund.findMany({
      ...RefundPrismaValidator,
      where: { paymentId, status: RefundStatus.PENDING },
    });

    return refunds.map((refund) => this.mapToEntity(refund));
  }

  async create(refund: RefundEntity): Promise<void> {
    await this.transaction(async () => {
      await this.prisma.refund.create({
        data: this.mapToDbRecord(refund),
      });

      await this.prisma.refundItem.createMany({
        data: refund
          .getProps()
          .items.map((refundItem) => this.mapToRefundItemDbRecord(refund.getId(), refundItem)),
      });
    });
  }

  async update(refund: RefundEntity): Promise<void> {
    await this.transaction(async () => {
      await this.prisma.refund.update({
        data: this.mapToDbRecord(refund),
        where: {
          refundId: refund.getId(),
        },
      });

      await this.updateRefundItems(refund.getId(), refund.getProps().items);
    });
  }

  private async updateRefundItems(refundId: string, refundItems: RefundItemEntity[]) {
    const updateRefundItems = refundItems.map(async (refundItem) =>
      this.prisma.refundItem.upsert({
        create: this.mapToRefundItemDbRecord(refundId, refundItem),
        update: this.mapToRefundItemDbRecord(refundId, refundItem),
        where: {
          refundItemId: refundItem.getId(),
        },
      }),
    );

    const updatedRefundItems = await Promise.all(updateRefundItems);

    await this.prisma.refundItem.deleteMany({
      where: {
        refundId,
        refundItemId: {
          notIn: updatedRefundItems.map((field) => field.refundItemId),
        },
      },
    });
  }

  private mapToRefundItemDbRecord(
    refundId: string,
    refundItem: RefundItemEntity,
  ): Prisma.RefundItemUncheckedCreateInput {
    const props = refundItem.getProps();

    return {
      refundId,
      orderItemId: props.orderItemId,
      refundItemId: props.id,
      refundedQuantity: props.refundedQuantity,
      refundedAmount: props.refundedAmount,
      refundedMarketplaceFee: props.refundedMarketplaceFee,
      createdAt: props.createdAt,
      modifiedAt: props.updatedAt,
    };
  }

  async delete(entity: RefundEntity): Promise<boolean> {
    throw new Error('Method not implemented.');
  }

  private mapToEntity(refund: RefundDbRecord): RefundEntity {
    return new RefundEntity({
      id: refund.refundId,
      createdAt: refund.createdAt,
      updatedAt: refund.modifiedAt,
      props: {
        idAtProvider: refund.idAtProvider,
        status: refund.status as RefundStatus,
        amount: refund.amount.toNumber(),
        paymentId: refund.paymentId,
        refundedMarketplaceFee: refund.refundedMarketplaceFee.toNumber(),
        refundedPaymentFee: refund.refundedPaymentFee.toNumber(),
        isManual: refund.isManual,
        items: refund.refundItems.map((refundItem) => this.mapRefundItemEntity(refundItem)),
      },
    });
  }

  private mapRefundItemEntity(refundItem: RefundDbRecord['refundItems'][number]) {
    return new RefundItemEntity({
      id: refundItem.refundItemId,
      createdAt: refundItem.createdAt,
      updatedAt: refundItem.modifiedAt,
      props: {
        refundedAmount: refundItem.refundedAmount.toNumber(),
        refundedMarketplaceFee: refundItem.refundedMarketplaceFee.toNumber(),
        refundedQuantity: refundItem.refundedQuantity,
        orderItemId: refundItem.orderItemId,
      },
    });
  }

  private mapToDbRecord(entity: RefundEntity): Prisma.RefundUncheckedCreateInput {
    const props = entity.getProps();

    return {
      refundId: props.id,
      idAtProvider: props.idAtProvider,
      status: props.status,
      amount: new Decimal(props.amount),
      refundedMarketplaceFee: new Decimal(props.refundedMarketplaceFee),
      refundedPaymentFee: new Decimal(props.refundedPaymentFee),
      paymentId: props.paymentId,
      isManual: props.isManual,
      createdAt: props.createdAt,
      modifiedAt: props.updatedAt,
    };
  }

  async findById(id: string): Promise<RefundEntity | null> {
    const refund = await this.prisma.refund.findFirst({
      ...RefundPrismaValidator,
      where: { refundId: id },
    });

    return refund ? this.mapToEntity(refund) : null;
  }

  async findAll(): Promise<RefundEntity[]> {
    throw new Error('Method not implemented.');
  }

  async findAllPaginated(params: PaginatedQueryParams): Promise<Paginated<RefundEntity>> {
    throw new Error('Method not implemented.');
  }
}
