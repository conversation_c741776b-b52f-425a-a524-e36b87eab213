import { Inject, Injectable } from '@nestjs/common';
import { checkRules } from '@sw-web/nestjs-core/domain';
import { EventBus } from '@sw-web/nestjs-core/event-bus';
import { groupBy, keyBy } from 'lodash';

import { FeeCalculatorApplicationService } from '@/modules/fee/application/services/fee-calculator.application-service';
import { OrderItemApplicationService } from '@/modules/order/application/services/order-item.service';
import { OrderEntity } from '@/modules/order/domain/order.entity';
import { PaymentEntity } from '@/modules/payment/domain/payment.entity';
import {
  PaymentCannotHavePendingRefundsRule,
  PaymentIsSucceededOrPartiallyRefundedRule,
  RefundAmountMustBeSmallerThanRemainingAmountRule,
  RefundMarketplaceFeeMustBeSmallerThanRemainingMarketplaceFeeRule,
} from '@/modules/payment/domain/rules';
import {
  PaymentProviderServicePort,
  ProviderFeeSettings,
} from '@/modules/payment-provider/domain/payment-provider-service.port';

import { RefundIssuedEvent } from '../../domain/events/refund-issued.event';
import { RefundItemEntity } from '../../domain/refund-item.entity';
import { RefundRepositoryPort } from '../../domain/refund-repository.port';
import { RefundEntity, RefundStatus } from '../../domain/refund.entity';
import { TotalRefundItemsEqualsRefundAmountRule } from '../../domain/rules/total-refund-items-equals-refund-amount.rule';
import { RefundNotFoundError } from '../../errors';

interface CreateRefundProps {
  payment: PaymentEntity;
  amount: number;
  marketplaceFee: number;
  paymentProviderAccountIdAtProvider: string;
  order: OrderEntity;
  items: {
    orderItemId: string;
    quantity: number;
    amount: number;
    marketplaceFee: number;
  }[];
}

interface CreateFullRefundProps
  extends Pick<CreateRefundProps, 'payment' | 'paymentProviderAccountIdAtProvider' | 'order'> {}

interface CreateManualRefundProps
  extends Pick<CreateRefundProps, 'payment' | 'amount' | 'marketplaceFee'> {
  refundIdAtProvider: string;
}

@Injectable()
export class RefundApplicationService {
  constructor(
    @Inject(PaymentProviderServicePort)
    private readonly paymentProviderService: PaymentProviderServicePort,
    @Inject(RefundRepositoryPort)
    private readonly refundRepository: RefundRepositoryPort,
    private readonly feeCalculatorService: FeeCalculatorApplicationService,
    private readonly eventBus: EventBus,
    private readonly orderItemApplicationService: OrderItemApplicationService,
  ) {}

  async markRefundIssued(refundIdAtProvider: string, payment: PaymentEntity, order: OrderEntity) {
    const refund = await this.getRefundByIdAtProvider(refundIdAtProvider);

    refund.update({
      status: RefundStatus.ISSUED,
    });

    await this.refundRepository.update(refund);

    await this.eventBus.publish(new RefundIssuedEvent({ refund, payment, order }));

    return refund;
  }

  private async getRefundByIdAtProvider(refundIdAtProvider: string) {
    const refund = await this.refundRepository.findByProviderId(refundIdAtProvider);

    if (!refund) {
      throw new RefundNotFoundError();
    }

    return refund;
  }

  async createManualRefund({
    payment,
    amount,
    refundIdAtProvider,
    marketplaceFee,
  }: CreateManualRefundProps) {
    const feesToRefund = this.calculateFeesToRefund({
      payment,
      amount,
      marketplaceFee,
    });

    const refund = RefundEntity.create({
      amount,
      paymentId: payment.getProps().id,
      idAtProvider: refundIdAtProvider,
      status: RefundStatus.PENDING,
      refundedMarketplaceFee: feesToRefund.marketplaceFee,
      refundedPaymentFee: feesToRefund.paymentFee,
      isManual: true,
      items: [],
    });

    await this.refundRepository.create(refund);

    return refund;
  }

  async createFullRefund(props: CreateFullRefundProps) {
    const { payment, order } = props;
    return this.createRefund({
      ...props,
      items: order.getProps().orderItems.map((orderItem) => ({
        orderItemId: orderItem.getId(),
        quantity: orderItem.getProps().currentQuantity,
        amount: orderItem.getProps().currentTotalAmount,
        marketplaceFee: orderItem.getProps().currentTotalMarketplaceFee,
      })),
      amount: payment.getProps().currentAmount,
      marketplaceFee: payment.getProps().currentMarketplaceFee,
    });
  }

  async createRefund({
    payment,
    amount: refundAmount,
    paymentProviderAccountIdAtProvider,
    order,
    items,
    marketplaceFee: marketplaceFeeToRefund,
  }: CreateRefundProps) {
    const pendingRefunds = await this.refundRepository.findPendingByPaymentId(payment.getId());

    checkRules(
      new PaymentIsSucceededOrPartiallyRefundedRule(payment.getProps().status),
      new PaymentCannotHavePendingRefundsRule(pendingRefunds),
      new RefundAmountMustBeSmallerThanRemainingAmountRule(
        payment.getProps().currentAmount,
        refundAmount,
        pendingRefunds,
      ),
      new RefundMarketplaceFeeMustBeSmallerThanRemainingMarketplaceFeeRule(
        payment.getProps().currentMarketplaceFee,
        marketplaceFeeToRefund,
        pendingRefunds,
      ),
      new TotalRefundItemsEqualsRefundAmountRule(refundAmount, marketplaceFeeToRefund, items),
    );

    const feesToRefund = this.calculateFeesToRefund({
      payment,
      amount: refundAmount,
      marketplaceFee: marketplaceFeeToRefund,
    });

    const refundItems = this.createRefundItems({
      items,
      order,
      pendingRefunds,
      providerFeeSettings: {
        fixed: payment.getProps().providerFeeFixed,
        percentage: payment.getProps().providerFeePercentage,
      },
    });

    const paymentProviderRefund = await this.paymentProviderService.createRefund({
      paymentId: payment.getProps().paymentIdAtProvider,
      amount: refundAmount,
      refundSplits:
        feesToRefund.netProfitToRefund !== 0
          ? [
              {
                accountId: paymentProviderAccountIdAtProvider,
                amount: feesToRefund.netProfitToRefund,
              },
            ]
          : [],
      currency: 'USD',
      marketplaceOrderFee: feesToRefund.marketplaceFee,
      platformOrderFee: 0,
    });

    const refund = RefundEntity.create({
      amount: refundAmount,
      paymentId: payment.getProps().id,
      idAtProvider: paymentProviderRefund.id,
      status: RefundStatus.PENDING,
      refundedMarketplaceFee: feesToRefund.marketplaceFee,
      refundedPaymentFee: feesToRefund.paymentFee,
      isManual: false,
      items: refundItems,
    });

    await this.refundRepository.transaction(async () => {
      await this.refundRepository.create(refund);
    });

    return refund;
  }

  createRefundItems({
    items,
    order,
    pendingRefunds,
    providerFeeSettings,
  }: Pick<CreateRefundProps, 'items' | 'order'> & {
    pendingRefunds: RefundEntity[];
    providerFeeSettings: ProviderFeeSettings;
  }) {
    const orderItemsById = keyBy(order.getProps().orderItems, (orderItem) => orderItem.getId());

    const refundItemsByOrderItemId = this.getPendingRefundItemsByOrderItemId(pendingRefunds);

    return items.map((item) => {
      const orderItem = orderItemsById[item.orderItemId];

      if (!orderItem) {
        throw new Error(`Order item not found by ID ${item.orderItemId}`);
      }

      const pendingRefundItems = refundItemsByOrderItemId[orderItem.getId()] || [];

      this.orderItemApplicationService.validateRefundItem({
        refundItem: {
          amount: item.amount,
          marketplaceFee: item.marketplaceFee,
          quantity: item.quantity,
        },
        orderItem,
        pendingRefundItems,
        providerFeeSettings,
      });

      return RefundItemEntity.create({
        orderItemId: orderItem.getId(),
        refundedAmount: item.amount,
        refundedMarketplaceFee: item.marketplaceFee,
        refundedQuantity: item.quantity,
      });
    });
  }

  private getPendingRefundItemsByOrderItemId(
    pendingRefunds: RefundEntity[],
  ): Record<string, RefundItemEntity[]> {
    const pendingRefundItems = pendingRefunds.flatMap((refund) => refund.getProps().items);

    return groupBy(
      pendingRefundItems,
      (pendingRefundItem) => pendingRefundItem.getProps().orderItemId,
    );
  }

  calculateFeesToRefund({
    payment,
    amount,
    marketplaceFee,
  }: Pick<CreateRefundProps, 'payment' | 'amount' | 'marketplaceFee'>) {
    const totalWithFeesAfterRefund = this.feeCalculatorService.calculateFeesAfterRefund({
      payment,
      refundAmount: amount,
      marketplaceFee,
    });

    const marketplaceFeeToRefund =
      payment.getProps().currentMarketplaceFee - totalWithFeesAfterRefund.marketplaceFee;

    const netProfitToRefund = payment.getProps().netAmount - totalWithFeesAfterRefund.netProfit;

    const paymentFeeToRefund =
      payment.getProps().currentPaymentProviderFee - totalWithFeesAfterRefund.paymentFee;

    return {
      netProfitToRefund,
      marketplaceFee: marketplaceFeeToRefund,
      paymentFee: paymentFeeToRefund,
    };
  }

  private;
}
