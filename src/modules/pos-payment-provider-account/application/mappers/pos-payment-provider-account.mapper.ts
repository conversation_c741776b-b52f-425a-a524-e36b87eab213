import { Inject } from '@nestjs/common';
import { PointOfSalePaymentProviderAccount as POSPaymentProviderAccountDbRecord } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { Mapper } from '@sw-web/nestjs-core/domain';

import { FeePayer } from '@/modules/fee/domain/fee-payer.enum';
import { PaymentProviderServicePort } from '@/modules/payment-provider/domain/payment-provider-service.port';

import { PosPaymentProviderAccountEntity } from '../../domain/pos-payment-provider-account.entity';
import { PosPaymentProviderAccountDto as POSPaymentProviderAccountUi } from '../../presentation/dto/pos-payment-provider-account.dto';

export class PosPaymentProviderAccountMapper
  implements
    Mapper<
      PosPaymentProviderAccountEntity,
      POSPaymentProviderAccountDbRecord,
      POSPaymentProviderAccountUi
    >
{
  constructor(
    @Inject(PaymentProviderServicePort)
    private readonly paymentProvider: PaymentProviderServicePort,
  ) {}

  toPersistence(entity: PosPaymentProviderAccountEntity): POSPaymentProviderAccountDbRecord {
    const props = entity.getProps();

    return {
      pointOfSalePaymentProviderAccountId: props.id,
      paymentProviderFeeFixed: props.paymentProviderFeeFixed
        ? new Decimal(props.paymentProviderFeeFixed)
        : null,
      paymentProviderFeePercentage: props.paymentProviderFeePercentage
        ? new Decimal(props.paymentProviderFeePercentage)
        : null,
      paymentProviderFeePayer: props.paymentProviderFeePayer,
      pointOfSaleId: props.pointOfSaleId,
      statementDescriptor: props.statementDescriptor,
      paymentProviderAccountId: props.paymentProviderAccountId,
      feeSettingsIdAtProvider: props.feeSettingsIdAtProvider,
      createdAt: props.createdAt,
      modifiedAt: props.updatedAt,
    };
  }

  toDomain(record: POSPaymentProviderAccountDbRecord): PosPaymentProviderAccountEntity {
    return new PosPaymentProviderAccountEntity({
      id: record.pointOfSalePaymentProviderAccountId,
      createdAt: record.createdAt,
      updatedAt: record.modifiedAt,
      props: {
        paymentProviderFeeFixed: record.paymentProviderFeeFixed?.toNumber() || null,
        paymentProviderFeePercentage: record.paymentProviderFeePercentage?.toNumber() || null,
        paymentProviderFeePayer: record.paymentProviderFeePayer as FeePayer,
        pointOfSaleId: record.pointOfSaleId,
        statementDescriptor: record.statementDescriptor,
        paymentProviderAccountId: record.paymentProviderAccountId,
        feeSettingsIdAtProvider: record.feeSettingsIdAtProvider,
      },
    });
  }

  toUi(entity: PosPaymentProviderAccountEntity): POSPaymentProviderAccountUi {
    const props = entity.getProps();

    return {
      id: props.id,
      paymentProviderFeeFixed:
        props.paymentProviderFeeFixed || this.paymentProvider.cardDefaultFee.fixed,
      paymentProviderFeePercentage:
        props.paymentProviderFeePercentage || this.paymentProvider.cardDefaultFee.percentage,
      paymentProviderFeePayer: props.paymentProviderFeePayer as FeePayer,
      pointOfSaleId: props.pointOfSaleId,
      statementDescriptor: props.statementDescriptor,
      paymentProviderAccountId: props.paymentProviderAccountId,
    };
  }
}
