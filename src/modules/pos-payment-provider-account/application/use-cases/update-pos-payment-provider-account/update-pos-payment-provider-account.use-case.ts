import { Inject, Injectable } from '@nestjs/common';
import { UseCase } from '@sw-web/nestjs-core/domain';
import { isUndefined, omitBy } from 'lodash';

import { FeePayer } from '@/modules/fee/domain/fee-payer.enum';
import { PaymentProviderServicePort } from '@/modules/payment-provider/domain/payment-provider-service.port';
import { PosPaymentProviderAccountRepositoryPort } from '@/modules/pos-payment-provider-account/domain/pos-payment-provider-account-repository.port';
import { PosPaymentProviderAccountEntity } from '@/modules/pos-payment-provider-account/domain/pos-payment-provider-account.entity';
import { PosPaymentProviderAccountNotFoundError } from '@/modules/pos-payment-provider-account/errors';
import { PosPaymentProviderAccountDto } from '@/modules/pos-payment-provider-account/presentation/dto/pos-payment-provider-account.dto';

import { PosPaymentProviderAccountMapper } from '../../mappers/pos-payment-provider-account.mapper';

interface Props {
  pointOfSaleId: string;
  paymentProviderAccountId: string;
  statementDescriptor?: string | null;
  paymentProviderFeeFixed?: number | null;
  paymentProviderFeePercentage?: number | null;
  paymentProviderFeePayer?: FeePayer;
}

@Injectable()
export class UpdatePosPaymentProviderAccountUseCase
  implements UseCase<Props, PosPaymentProviderAccountDto>
{
  constructor(
    @Inject(PosPaymentProviderAccountRepositoryPort)
    private readonly posPaymentProviderAccountRepository: PosPaymentProviderAccountRepositoryPort,
    private readonly posPaymentProviderAccountMapper: PosPaymentProviderAccountMapper,
    @Inject(PaymentProviderServicePort)
    private readonly paymentProviderService: PaymentProviderServicePort,
  ) {}

  async execute(props: Props) {
    const posPaymentProviderAccount =
      await this.posPaymentProviderAccountRepository.findByPosIdAndPaymentProviderAccountId(
        props.pointOfSaleId,
        props.paymentProviderAccountId,
      );

    if (!posPaymentProviderAccount) {
      throw new PosPaymentProviderAccountNotFoundError();
    }

    const updateData = omitBy(
      {
        statementDescriptor: props.statementDescriptor,
        paymentProviderFeeFixed: props.paymentProviderFeeFixed,
        paymentProviderFeePercentage: props.paymentProviderFeePercentage,
        paymentProviderFeePayer: props.paymentProviderFeePayer,
      },
      isUndefined,
    );

    if (updateData.paymentProviderFeeFixed || updateData.paymentProviderFeePercentage) {
      const feeSettingsAtProvider = await this.createOrUpdateFeeSettings(
        posPaymentProviderAccount,
        updateData,
      );

      updateData.feeSettingsIdAtProvider = feeSettingsAtProvider.id;
    }

    posPaymentProviderAccount.update(updateData);

    await this.posPaymentProviderAccountRepository.update(posPaymentProviderAccount);

    return this.posPaymentProviderAccountMapper.toUi(posPaymentProviderAccount);
  }

  private async createOrUpdateFeeSettings(
    posPaymentProviderAccount: PosPaymentProviderAccountEntity,
    feeSettingsParams: Pick<Props, 'paymentProviderFeeFixed' | 'paymentProviderFeePercentage'>,
  ) {
    const data = {
      marketplaceFeeFixed:
        feeSettingsParams.paymentProviderFeeFixed ||
        posPaymentProviderAccount.getProps().paymentProviderFeeFixed ||
        this.paymentProviderService.cardDefaultFee.fixed,
      marketplaceFeePercentage:
        feeSettingsParams.paymentProviderFeePercentage ||
        posPaymentProviderAccount.getProps().paymentProviderFeePercentage ||
        this.paymentProviderService.cardDefaultFee.percentage,
    };

    if (posPaymentProviderAccount.getProps().feeSettingsIdAtProvider) {
      return this.paymentProviderService.updateFeeSettings({
        feeSettingsIdAtProvider: posPaymentProviderAccount.getProps()
          .feeSettingsIdAtProvider as string,
        ...data,
      });
    }

    return this.paymentProviderService.createFeeSettings({
      ...data,
    });
  }
}
