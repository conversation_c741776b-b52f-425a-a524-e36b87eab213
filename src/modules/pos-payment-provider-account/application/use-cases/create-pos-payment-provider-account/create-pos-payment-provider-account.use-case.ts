import { Inject, Injectable } from '@nestjs/common';
import { UseCase } from '@sw-web/nestjs-core/domain';

import { FeePayer } from '@/modules/fee/domain/fee-payer.enum';
import { PaymentProviderServicePort } from '@/modules/payment-provider/domain/payment-provider-service.port';
import { PaymentProviderAccountRepositoryPort } from '@/modules/payment-provider-account/domain/payment-provider-account-repository.port';
import { PaymentProviderAccountNotFoundError } from '@/modules/payment-provider-account/errors';
import { PointOfSaleRepositoryPort } from '@/modules/point-of-sale/domain/point-of-sale-repository.port';
import { PointOfSaleNotFoundError } from '@/modules/point-of-sale/errors';
import { PosPaymentProviderAccountRepositoryPort } from '@/modules/pos-payment-provider-account/domain/pos-payment-provider-account-repository.port';
import { PosPaymentProviderAccountEntity } from '@/modules/pos-payment-provider-account/domain/pos-payment-provider-account.entity';
import { PosPaymentProviderAccountDto } from '@/modules/pos-payment-provider-account/presentation/dto/pos-payment-provider-account.dto';

import { PosPaymentProviderAccountMapper } from '../../mappers/pos-payment-provider-account.mapper';

interface Props {
  pointOfSaleId: string;
  paymentProviderAccountId: string;
  statementDescriptor: string | null;
  paymentProviderFeeFixed: number | null;
  paymentProviderFeePercentage: number | null;
  paymentProviderFeePayer: FeePayer;
}

@Injectable()
export class CreatePosPaymentProviderAccountUseCase
  implements UseCase<Props, PosPaymentProviderAccountDto>
{
  constructor(
    @Inject(PosPaymentProviderAccountRepositoryPort)
    private readonly posPaymentProviderAccountRepository: PosPaymentProviderAccountRepositoryPort,
    @Inject(PointOfSaleRepositoryPort)
    private readonly posRepository: PointOfSaleRepositoryPort,
    @Inject(PaymentProviderAccountRepositoryPort)
    private readonly paymentProviderAccountRepository: PaymentProviderAccountRepositoryPort,
    private readonly posPaymentProviderAccountMapper: PosPaymentProviderAccountMapper,
    @Inject(PaymentProviderServicePort)
    private readonly paymentProviderService: PaymentProviderServicePort,
  ) {}

  async execute(props: Props) {
    const pointOfSale = await this.posRepository.findById(props.pointOfSaleId);

    if (!pointOfSale) {
      throw new PointOfSaleNotFoundError();
    }
    const paymentProviderAccount = await this.paymentProviderAccountRepository.findById(
      props.paymentProviderAccountId,
    );

    if (!paymentProviderAccount) {
      throw new PaymentProviderAccountNotFoundError();
    }

    const feeSettings = await this.paymentProviderService.createFeeSettings({
      marketplaceFeeFixed:
        props.paymentProviderFeeFixed || this.paymentProviderService.cardDefaultFee.fixed,
      marketplaceFeePercentage:
        props.paymentProviderFeePercentage || this.paymentProviderService.cardDefaultFee.percentage,
    });

    const posPaymentProviderAccount = PosPaymentProviderAccountEntity.create({
      pointOfSaleId: props.pointOfSaleId,
      paymentProviderAccountId: props.paymentProviderAccountId,
      statementDescriptor: props.statementDescriptor,
      paymentProviderFeeFixed:
        props.paymentProviderFeeFixed || this.paymentProviderService.cardDefaultFee.fixed,
      paymentProviderFeePercentage:
        props.paymentProviderFeePercentage || this.paymentProviderService.cardDefaultFee.percentage,
      paymentProviderFeePayer: props.paymentProviderFeePayer,
      feeSettingsIdAtProvider: feeSettings.id,
    });

    await this.posPaymentProviderAccountRepository.create(posPaymentProviderAccount);

    return this.posPaymentProviderAccountMapper.toUi(posPaymentProviderAccount);
  }
}
