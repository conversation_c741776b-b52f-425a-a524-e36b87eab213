import { Entity } from '@sw-web/nestjs-core/domain';

import { FeePayer } from '@/modules/fee/domain/fee-payer.enum';

interface Props {
  paymentProviderAccountId: string;
  feeSettingsIdAtProvider: string | null;
  pointOfSaleId: string;
  paymentProviderFeeFixed: number | null;
  paymentProviderFeePercentage: number | null;
  statementDescriptor: string | null;
  paymentProviderFeePayer: FeePayer;
}

export class PosPaymentProviderAccountEntity extends Entity<Props> {
  static create(props: Props) {
    return new PosPaymentProviderAccountEntity({ props });
  }
}
