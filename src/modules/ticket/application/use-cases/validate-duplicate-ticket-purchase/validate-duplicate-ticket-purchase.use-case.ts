import { Inject, Injectable } from '@nestjs/common';
import { TicketType } from '@prisma/client';
import { checkRules, UseCase } from '@sw-web/nestjs-core/domain';
import { groupBy, keyBy, mapValues, sumBy, uniq } from 'lodash';

import { OrderItemHolderEntity } from '@/modules/order/domain/order-item-holder.entity';
import { OrderItemEntity } from '@/modules/order/domain/order-item.entity';
import { OrderRepositoryPort } from '@/modules/order/domain/order-repository.port';
import { OrderEntity } from '@/modules/order/domain/order.entity';
import { OrderNotFoundError } from '@/modules/order/errors';
import { PointOfSaleRepositoryPort } from '@/modules/point-of-sale/domain/point-of-sale-repository.port';
import { PointOfSaleEntity } from '@/modules/point-of-sale/domain/point-of-sale.entity';
import {
  PointOfSaleNotFoundError,
  PointOfSaleScheduleIncompleteError,
} from '@/modules/point-of-sale/errors';
import { DuplicateTicketPurchaseRule } from '@/modules/ticket/domain/rules';
import { TicketRepositoryPort } from '@/modules/ticket/domain/ticket-repository.port';
import { TicketEntity } from '@/modules/ticket/domain/ticket.entity';

interface Props {
  fingerprintAtProvider: string;
  orderId: string;
}

@Injectable()
export class ValidateDuplicateTicketPurchaseUseCase implements UseCase<Props, void> {
  constructor(
    @Inject(OrderRepositoryPort)
    private readonly orderRepository: OrderRepositoryPort,
    @Inject(PointOfSaleRepositoryPort)
    private readonly pointOfSaleRepository: PointOfSaleRepositoryPort,
    @Inject(TicketRepositoryPort)
    private readonly ticketRepository: TicketRepositoryPort,
  ) {}

  async execute(props: Props) {
    const order = await this.orderRepository.findById(props.orderId);

    if (!order) {
      throw new OrderNotFoundError();
    }

    const pointOfSale = await this.pointOfSaleRepository.findById(order.getProps().pointOfSaleId);

    if (!pointOfSale) {
      throw new PointOfSaleNotFoundError();
    }

    const paidOrders = await this.orderRepository.findPaidOrdersByPosId(pointOfSale.getId(), {
      fingerprintAtProvider: props.fingerprintAtProvider,
      email: order.getProps().customerInfo.email,
      phone: order.getProps().customerInfo.phone,
    });

    await this.validateDuplicatePurchase(order, paidOrders, pointOfSale);
  }

  private calculateEventDaysCount(pointOfSale: PointOfSaleEntity) {
    const { dateEnd, dateStart } = pointOfSale.getProps();

    if (!dateStart || !dateEnd) {
      throw new PointOfSaleScheduleIncompleteError('Event start or end date is not defined');
    }

    const diffInMilliseconds = dateEnd.getTime() - dateStart.getTime();

    const diffInDays = diffInMilliseconds / (1000 * 60 * 60 * 24);

    return Math.ceil(diffInDays) + 1;
  }

  private sumTicketQuantities(
    ticketsByProductId: Record<string, TicketEntity>,
    orderItems: OrderItemEntity[],
  ): Record<TicketType, number> {
    return mapValues(
      groupBy(
        orderItems,
        (item) => ticketsByProductId[item.getProps().productId]?.getProps().ticketType,
      ),
      (items) => sumBy(items, (item) => item.getProps().currentQuantity),
    ) as Record<TicketType, number>;
  }

  private async validateDuplicatePurchase(
    order: OrderEntity,
    paidOrders: OrderEntity[],
    pointOfSale: PointOfSaleEntity,
  ) {
    const tickets = await this.getAllRelatedTickets(order, paidOrders);
    const ticketsByProductId = keyBy(tickets, (product) => product.getId());

    const daysInEvent = this.calculateEventDaysCount(pointOfSale);

    const { orderItems } = order.getProps();

    orderItems.forEach((item) => {
      const { holder } = item.getProps();

      if (!holder) {
        return;
      }

      const paidOrderItemsWithSameHolder: OrderItemEntity[] = [];

      paidOrders.forEach((paidOrder) => {
        paidOrder.getProps().orderItems.forEach((paidItem) => {
          const paidItemHolder = paidItem.getProps().holder;

          if (paidItemHolder && this.isSameHolder(paidItemHolder, holder)) {
            paidOrderItemsWithSameHolder.push(paidItem);
          }
        });
      });

      const alreadyBought = this.sumTicketQuantities(
        ticketsByProductId,
        paidOrderItemsWithSameHolder,
      );

      const beingBought = this.sumTicketQuantities(ticketsByProductId, [item]);

      checkRules(
        new DuplicateTicketPurchaseRule(alreadyBought, beingBought, daysInEvent, {
          firstName: holder.getProps().firstName as string,
          lastName: holder.getProps().lastName as string,
          eventName: pointOfSale.getProps().name,
        }),
      );
    });
  }

  private isSameHolder(holder1: OrderItemHolderEntity, holder2: OrderItemHolderEntity): boolean {
    return (
      this.sameName(holder1.getProps().firstName, holder2.getProps().firstName) &&
      this.sameName(holder1.getProps().lastName, holder2.getProps().lastName)
    );
  }

  private sameName(name1: string | null, name2: string | null): boolean {
    if (!name1 || !name2) {
      return false;
    }

    return name1.trim().toLowerCase() === name2.trim().toLowerCase();
  }

  private async getAllRelatedTickets(order: OrderEntity, earlierPaidOrders: OrderEntity[]) {
    const productIds = earlierPaidOrders.flatMap((o) =>
      o.getProps().orderItems.map((item) => item.getProps().productId),
    );

    productIds.push(...order.getProps().orderItems.map((item) => item.getProps().productId));

    const uniqueProductIds = uniq(productIds);

    const tickets = await this.ticketRepository.findByIds(uniqueProductIds);

    if (tickets.length !== uniqueProductIds.length) {
      throw new Error('Some products were not found');
    }

    return tickets;
  }
}
