import { Body, Controller, Get, Param, Post, Put, UseGuards } from '@nestjs/common';

import { MarketplaceApiKeyGuard } from '@/infra/guards';

import { CreateTicketDto } from './dto/create-ticket.dto';
import { UpdateTicketDto } from './dto/update-ticket.dto';
import { ValidateDuplicateTicketPurchaseDto } from './dto/validate-duplicate-ticket-purchase.dto';
import { CreateTicketUseCase } from '../application/use-cases/create-ticket/create-ticket.use-case';
import { GetTicketUseCase } from '../application/use-cases/get-ticket/get-ticket.use-case';
import { ListTicketUseCase } from '../application/use-cases/list-ticket/list-ticket.use-case';
import { UpdateTicketUseCase } from '../application/use-cases/update-ticket/update-ticket.use-case';
import { ValidateDuplicateTicketPurchaseUseCase } from '../application/use-cases/validate-duplicate-ticket-purchase/validate-duplicate-ticket-purchase.use-case';

@Controller()
export class TicketController {
  constructor(
    private readonly createTicket: CreateTicketUseCase,
    private readonly updateTicket: UpdateTicketUseCase,
    private readonly getTicket: GetTicketUseCase,
    private readonly listTicket: ListTicketUseCase,
    private readonly validateDuplicateTicketPurchase: ValidateDuplicateTicketPurchaseUseCase,
  ) {}

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('point-of-sales/:pointOfSaleId/tickets')
  public async create(@Body() dto: CreateTicketDto, @Param('pointOfSaleId') pointOfSaleId: string) {
    return this.createTicket.execute({
      ...dto,
      pointOfSaleId,
    });
  }

  @Post('point-of-sales/:pointOfSaleId/tickets/validate-duplicate-purchase')
  public async validateDuplicatePurchase(@Body() dto: ValidateDuplicateTicketPurchaseDto) {
    return this.validateDuplicateTicketPurchase.execute({
      fingerprintAtProvider: dto.fingerprintAtProvider,
      orderId: dto.orderId,
    });
  }

  @Get('point-of-sales/:pointOfSaleId/tickets')
  public async list(@Param('pointOfSaleId') pointOfSaleId: string) {
    return this.listTicket.execute({
      pointOfSaleId,
    });
  }

  // TODO: ADD ACCESS CONTROL
  @UseGuards(MarketplaceApiKeyGuard)
  @Put('point-of-sales/:pointOfSaleId/tickets/:productId')
  public async update(
    @Body() dto: UpdateTicketDto,
    @Param('pointOfSaleId') pointOfSaleId: string,
    @Param('productId') productId: string,
  ) {
    return this.updateTicket.execute({
      ...dto,
      productId,
      pointOfSaleId,
    });
  }

  @Get('point-of-sales/:pointOfSaleId/tickets/:productId')
  public async get(
    @Param('pointOfSaleId') pointOfSaleId: string,
    @Param('productId') productId: string,
  ) {
    return this.getTicket.execute({
      productId,
      pointOfSaleId,
    });
  }
}
