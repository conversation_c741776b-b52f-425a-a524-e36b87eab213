import { Module } from '@nestjs/common';

import { MarketplaceModule } from '@/modules/marketplace/infra/framework/marketplace.module';
import { OrderModule } from '@/modules/order/infra/framework/order.module';
import { PointOfSaleModule } from '@/modules/point-of-sale/infra/framework/point-of-sale.module';
import { ProductCategoryModule } from '@/modules/product-category/infra/framework/product-category.module';

import { TicketMapper } from '../../application/mappers/ticket.mapper';
import { TICKET_USE_CASES } from '../../application/use-cases';
import { TicketRepositoryPort } from '../../domain/ticket-repository.port';
import { TicketController } from '../../presentation/ticket.controller';
import { PrismaTicketRepositoryAdapter } from '../repositories/prisma-ticket-repository.adapter';

const REPOSITORIES = [
  {
    provide: TicketRepositoryPort,
    useClass: PrismaTicketRepositoryAdapter,
  },
];

@Module({
  imports: [MarketplaceModule, PointOfSaleModule, ProductCategoryModule, OrderModule],
  controllers: [TicketController],
  providers: [TicketMapper, ...TICKET_USE_CASES, ...REPOSITORIES],
  exports: [...REPOSITORIES],
})
export class TicketModule {}
