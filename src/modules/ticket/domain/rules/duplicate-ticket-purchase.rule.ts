/* ---------------------------------------------------- */
/* rules/ticket-purchase-validation.rule.ts             */
/* ---------------------------------------------------- */
import { TicketType } from '@prisma/client';
import { IBusinessRule, DomainError } from '@sw-web/nestjs-core/domain';

import {
  DuplicateDailyTicketPurchaseError,
  DuplicateWeekendTicketPurchaseError,
} from '../../errors';

interface MessageParams {
  firstName: string;
  lastName: string;
  eventName: string;
}

export class DuplicateTicketPurchaseRule implements IBusinessRule {
  private error: DomainError | null = null;

  constructor(
    private readonly alreadyBought: Record<TicketType, number>,
    private readonly beingBought: Record<TicketType, number>,
    private readonly eventDaysCount: number,
    private readonly messageParams: MessageParams,
  ) {}

  /* -------------- IBusinessRule implementation ----------------------- */

  isBroken(): boolean {
    const weekendBought = this.alreadyBought[TicketType.weekend] ?? 0;
    const weekendBuying = this.beingBought[TicketType.weekend] ?? 0;
    const dailyBought = this.alreadyBought[TicketType.daily] ?? 0;
    const dailyBuying = this.beingBought[TicketType.daily] ?? 0;

    if (weekendBuying > 0) {
      const newWeekendQty = weekendBought + weekendBuying;

      if (newWeekendQty > 1) {
        this.error = new DuplicateWeekendTicketPurchaseError(this.getWeekendErrorMessage());
        return true;
      }

      if (dailyBought > 0) {
        this.error = new DuplicateDailyTicketPurchaseError(this.getDailyErrorMessage(dailyBought));
        return true;
      }
    }

    if (dailyBuying > 0) {
      const newDailyQty = dailyBought + dailyBuying;

      if (weekendBought > 0) {
        this.error = new DuplicateWeekendTicketPurchaseError(this.getWeekendErrorMessage());
        return true;
      }

      if (newDailyQty > this.eventDaysCount) {
        this.error = new DuplicateDailyTicketPurchaseError(this.getDailyErrorMessage(dailyBought));
        return true;
      }
    }

    return false;
  }

  getError(): DomainError {
    return this.error!;
  }

  private getWeekendErrorMessage() {
    const { firstName: first, lastName: last, eventName } = this.messageParams;
    return `You have already purchased a weekend ticket for ${first} ${last} for ${eventName}. Are you sure you want to buy more?`;
  }

  private getDailyErrorMessage(dailyTicketsCount: number) {
    const { firstName: first, lastName: last, eventName } = this.messageParams;
    return `You have already purchased ${dailyTicketsCount} daily tickets for ${first} ${last} for ${eventName}. Are you sure you want to buy more?`;
  }
}
