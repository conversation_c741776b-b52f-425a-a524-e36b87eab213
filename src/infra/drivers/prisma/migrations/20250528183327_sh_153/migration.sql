-- Populate the new columns with data from the Customer table
UPDATE "Order"
SET "customerPhone" = "Customer"."phone",
    "customerEmail" = "Customer"."email",
    "customerFirstName" = "Customer"."firstName",
    "customerLastName" = "Customer"."lastName",
    "customerCountry" = "Customer"."country",
    "customerZip" = "Customer"."zip"
FROM "Customer"
WHERE "Order"."customerId" = "Customer"."customerId";