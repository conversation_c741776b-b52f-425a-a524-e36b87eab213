/*
  Warnings:

  - Made the column `customerCountry` on table `Order` required. This step will fail if there are existing NULL values in that column.
  - Made the column `customerEmail` on table `Order` required. This step will fail if there are existing NULL values in that column.
  - Made the column `customerFirstName` on table `Order` required. This step will fail if there are existing NULL values in that column.
  - Made the column `customerLastName` on table `Order` required. This step will fail if there are existing NULL values in that column.
  - Made the column `customerPhone` on table `Order` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Order" ALTER COLUMN "customerCountry" SET NOT NULL,
ALTER COLUMN "customerEmail" SET NOT NULL,
ALTER COLUMN "customerFirstName" SET NOT NULL,
ALTER COLUMN "customerLastName" SET NOT NULL,
ALTER COLUMN "customerPhone" SET NOT NULL;
