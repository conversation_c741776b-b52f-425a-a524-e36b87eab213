generator serverClient {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model ProductCategory {
  productCategoryId String   @id
  name              String   @unique
  description       String?
  createdAt         DateTime @default(now())
  modifiedAt        DateTime @updatedAt

  products Product[]
}

model Marketplace {
  marketplaceId String   @id
  name          String
  description   String?
  apiKey        String   @unique
  createdAt     DateTime @default(now())
  modifiedAt    DateTime @updatedAt

  pointOfSales           PointOfSale[]
  paymentProviderAccount PaymentProviderAccount[]
  webhooksEndpoints      MarketplaceWebhookEndpoint[]
  webhookEvents          WebhookEvent[]
  bans                   Ban[]
  banWhitelists          BanWhitelist[]
}

model Product {
  productId         String   @id
  productCategoryId String
  initialPrice      Decimal  @default(0) @db.Decimal(10, 2)
  currentPrice      Decimal  @default(0) @db.Decimal(10, 2)
  marketplaceFee    Decimal  @default(0) @db.Decimal(10, 2)
  description       String?
  label             String
  shortLabel        String?
  createdAt         DateTime @default(now())
  modifiedAt        DateTime @updatedAt

  ticketSettings      TicketSettings?
  ticketGuruSettings  TicketGuruSettings?
  priceChanges        ProductPriceChange[]
  pointOfSaleProducts PointOfSaleProduct[]
  orderItems          OrderItem[]

  productCategory ProductCategory @relation(fields: [productCategoryId], references: [productCategoryId])
}

model TicketSettings {
  ticketSettingsId String     @id
  ticketType       TicketType
  validDates       Json       @default("[]")
  productId        String     @unique
  createdAt        DateTime   @default(now())
  modifiedAt       DateTime   @updatedAt

  product Product @relation(fields: [productId], references: [productId])
}

enum TicketType {
  daily
  weekend
  season
}

model TicketGuruSettings {
  ticketGuruSettingsId String   @id
  litePin              String?
  buyPin               String?
  adminPin             String?
  purchasePasscode     String?
  refundPasscode       String?
  locations            Json?
  productId            String   @unique
  createdAt            DateTime @default(now())
  modifiedAt           DateTime @updatedAt

  product Product @relation(fields: [productId], references: [productId])
}

model ProductPriceChange {
  productPriceChangeId String   @id
  changeDate           DateTime
  productId            String
  price                Decimal  @default(0) @db.Decimal(10, 2)
  createdAt            DateTime @default(now())
  modifiedAt           DateTime @updatedAt

  product Product @relation(fields: [productId], references: [productId])
}

model PointOfSaleProduct {
  pointOfSaleId String
  productId     String
  published     Boolean  @default(false)
  createdAt     DateTime @default(now())
  modifiedAt    DateTime @updatedAt

  pointOfSales PointOfSale @relation(fields: [pointOfSaleId], references: [pointOfSaleId])
  product      Product     @relation(fields: [productId], references: [productId])

  @@id(name: "pointOfSaleProductId", [pointOfSaleId, productId])
}

model PointOfSale {
  pointOfSaleId       String    @id
  name                String
  shortName           String
  marketplaceFee      Decimal   @default(0) @db.Decimal(10, 2)
  marketplaceFeePayer String    @default("seller")
  zip                 String?
  address             String?
  country             String?
  state               String?
  phone               String?
  email               String?
  city                String?
  website             String?
  location            Json?     @default("[]")
  timezone            String    @default("America/New_York")
  description         String?
  disclaimer          String?
  published           Boolean   @default(false)
  dateStart           DateTime?
  dateEnd             DateTime?
  marketplaceId       String
  salesStartDate      DateTime?
  salesEndDate        DateTime?
  entryCodeRequired   Boolean   @default(false)
  customSettings      Json      @default("{}")
  createdAt           DateTime  @default(now())
  modifiedAt          DateTime  @updatedAt

  marketplace         Marketplace                         @relation(fields: [marketplaceId], references: [marketplaceId])
  customFields        CustomField[]
  pointOfSaleProducts PointOfSaleProduct[]
  paymentMethods      PointOfSalePaymentProviderAccount[]
  orders              Order[]
}

model CustomField {
  customFieldId String   @id
  type          String
  label         String
  shortLabel    String?
  required      Boolean  @default(false)
  order         Int
  options       Json?
  pointOfSaleId String
  isHidden      Boolean  @default(false)
  createdAt     DateTime @default(now())
  modifiedAt    DateTime @updatedAt

  pointOfSales      PointOfSale        @relation(fields: [pointOfSaleId], references: [pointOfSaleId])
  orderCustomFields OrderCustomField[]
}

model OrderCustomField {
  orderCustomFieldId String   @id
  value              String
  customFieldId      String
  orderId            String
  createdAt          DateTime @default(now())
  modifiedAt         DateTime @updatedAt

  customField CustomField @relation(fields: [customFieldId], references: [customFieldId])
  order       Order       @relation(fields: [orderId], references: [orderId])

  @@unique([orderId, customFieldId])
}

model PaymentProviderAccount {
  paymentProviderAccountId String   @id
  paymentProviderType      String
  idAtProvider             String
  isActive                 Boolean  @default(true)
  marketplaceId            String
  createdAt                DateTime @default(now())
  modifiedAt               DateTime @updatedAt

  marketplace                         Marketplace                         @relation(fields: [marketplaceId], references: [marketplaceId])
  pointOfSalesPaymentProviderAccounts PointOfSalePaymentProviderAccount[]
}

model PaymentProviderCustomer {
  paymentProviderCustomerId String   @id
  paymentProviderType       String
  idAtProvider              String
  customerId                String
  createdAt                 DateTime @default(now())
  modifiedAt                DateTime @updatedAt

  customer Customer @relation(fields: [customerId], references: [customerId])

  @@unique([customerId, idAtProvider])
}

model PointOfSalePaymentProviderAccount {
  pointOfSalePaymentProviderAccountId String   @id
  paymentProviderAccountId            String
  pointOfSaleId                       String
  paymentProviderFeeFixed             Decimal? @db.Decimal(10, 2)
  paymentProviderFeePercentage        Decimal? @db.Decimal(5, 4)
  statementDescriptor                 String?
  paymentProviderFeePayer             String
  feeSettingsIdAtProvider             String?
  createdAt                           DateTime @default(now())
  modifiedAt                          DateTime @updatedAt

  paymentProviderAccount PaymentProviderAccount @relation(fields: [paymentProviderAccountId], references: [paymentProviderAccountId])
  pointOfSales           PointOfSale            @relation(fields: [pointOfSaleId], references: [pointOfSaleId])

  @@unique([pointOfSaleId, paymentProviderAccountId])
}

model Order {
  orderId        String  @id
  status         String
  initialAmount  Decimal @db.Decimal(10, 2)
  currentAmount  Decimal @db.Decimal(10, 2)
  subtotal       Decimal @db.Decimal(10, 2)
  entryCode      String?
  customerId     String
  pointOfSaleId  String
  marketplaceFee Decimal @db.Decimal(10, 2)

  customerPhone     String
  customerEmail     String
  customerFirstName String
  customerLastName  String
  customerCountry   String
  customerZip       String?

  recipientEmail String

  createdAt  DateTime @default(now())
  modifiedAt DateTime @updatedAt

  customer      Customer           @relation(fields: [customerId], references: [customerId])
  orderItems    OrderItem[]
  payments      Payment[]
  customFields  OrderCustomField[]
  pointOfSale   PointOfSale        @relation(fields: [pointOfSaleId], references: [pointOfSaleId])
  webhookEvents WebhookEvent[]
}

model Customer {
  customerId String   @id
  firstName  String?
  lastName   String?
  email      String?
  phone      String?
  address    String?
  zip        String?
  country    String?
  createdAt  DateTime @default(now())
  modifiedAt DateTime @updatedAt

  orders                  Order[]
  PaymentProviderCustomer PaymentProviderCustomer[]
}

model OrderItem {
  orderItemId                String   @id
  orderId                    String
  price                      Decimal  @db.Decimal(10, 2)
  initialQuantity            Int
  currentQuantity            Int
  initialTotalAmount         Decimal  @db.Decimal(10, 2)
  currentTotalAmount         Decimal  @db.Decimal(10, 2)
  subtotalAmount             Decimal  @db.Decimal(10, 2)
  initialTotalMarketplaceFee Decimal  @db.Decimal(10, 2)
  currentTotalMarketplaceFee Decimal  @db.Decimal(10, 2)
  productId                  String
  marketplaceFee             Decimal  @db.Decimal(10, 2)
  createdAt                  DateTime @default(now())
  modifiedAt                 DateTime @updatedAt

  order       Order             @relation(fields: [orderId], references: [orderId])
  product     Product           @relation(fields: [productId], references: [productId])
  qrCodes     OrderItemQrCode[]
  itemHolder  OrderItemHolder?
  refundItems RefundItem[]
}

model Payment {
  paymentId                 String   @id
  orderId                   String
  initialAmount             Decimal  @db.Decimal(10, 2)
  currentAmount             Decimal  @db.Decimal(10, 2)
  paymentIdAtProvider       String
  paymentProviderType       String
  type                      String
  status                    String
  totalFeeAmount            Decimal  @db.Decimal(10, 2)
  netAmount                 Decimal  @db.Decimal(10, 2)
  initialPaymentProviderFee Decimal  @default(0) @db.Decimal(10, 2)
  currentPaymentProviderFee Decimal  @default(0) @db.Decimal(10, 2)
  currentMarketplaceFee     Decimal  @default(0) @db.Decimal(10, 2)
  initialMarketplaceFee     Decimal  @default(0) @db.Decimal(10, 2)
  providerFeeFixed          Decimal  @db.Decimal(10, 2)
  providerFeePercentage     Decimal  @db.Decimal(5, 4)
  paymentProviderFeePayer   String
  marketplaceFeePayer       String
  paymentSessionId          String?
  paymentMethodId           String?
  clientInfo                Json?
  createdAt                 DateTime @default(now())
  modifiedAt                DateTime @updatedAt

  order         Order          @relation(fields: [orderId], references: [orderId])
  refunds       Refund[]
  paymentMethod PaymentMethod? @relation(fields: [paymentMethodId], references: [paymentMethodId])
  webhookEvents WebhookEvent[]
}

model OrderItemQrCode {
  orderItemQrCodeId String   @id
  orderItemId       String   @unique
  version           Decimal  @db.Decimal(10, 2)
  filePath          String
  active            Boolean  @default(false)
  createdAt         DateTime @default(now())
  modifiedAt        DateTime @updatedAt

  orderItem OrderItem @relation(fields: [orderItemId], references: [orderItemId])
}

model Refund {
  refundId               String   @id
  paymentId              String
  amount                 Decimal  @default(0) @db.Decimal(10, 2)
  refundedMarketplaceFee Decimal  @default(0) @db.Decimal(10, 2)
  refundedPaymentFee     Decimal  @default(0) @db.Decimal(10, 2)
  status                 String
  idAtProvider           String
  isManual               Boolean  @default(false)
  createdAt              DateTime @default(now())
  modifiedAt             DateTime @updatedAt

  payment       Payment        @relation(fields: [paymentId], references: [paymentId])
  refundItems   RefundItem[]
  webhookEvents WebhookEvent[]
}

model RefundItem {
  refundItemId           String   @id
  refundId               String
  orderItemId            String
  refundedAmount         Decimal  @default(0) @db.Decimal(10, 2)
  refundedMarketplaceFee Decimal  @default(0) @db.Decimal(10, 2)
  refundedQuantity       Int
  createdAt              DateTime @default(now())
  modifiedAt             DateTime @updatedAt

  refund    Refund    @relation(fields: [refundId], references: [refundId])
  orderItem OrderItem @relation(fields: [orderItemId], references: [orderItemId])
}

model OrderItemHolder {
  orderItemHolderId String   @id
  orderItemId       String   @unique
  firstName         String?
  lastName          String?
  phone             String?
  email             String?
  address           String?
  zip               String?
  country           String?
  state             String?
  createdAt         DateTime @default(now())
  modifiedAt        DateTime @updatedAt

  orderItem OrderItem @relation(fields: [orderItemId], references: [orderItemId])
}

model MarketplaceWebhookEndpoint {
  marketplaceWebhookEndpointId String   @id
  marketplaceId                String
  url                          String
  webhookSecret                String
  createdAt                    DateTime @default(now())
  modifiedAt                   DateTime @updatedAt

  marketplace   Marketplace    @relation(fields: [marketplaceId], references: [marketplaceId])
  webhookEvents WebhookEvent[]
}

model PaymentSession {
  paymentSessionId           String   @id
  amount                     Decimal  @db.Decimal(10, 2)
  orderId                    String
  paymentIntentIdAtProvider  String   @unique
  paymentProviderType        String
  paymentProviderPaymentType String
  paymentFee                 Decimal  @db.Decimal(10, 2)
  marketplaceFee             Decimal  @db.Decimal(10, 2)
  providerFeeFixed           Decimal  @db.Decimal(10, 2)
  providerFeePercentage      Decimal  @db.Decimal(5, 4)
  paymentFeePayer            String
  marketplaceFeePayer        String
  clientInfo                 Json?
  createdAt                  DateTime @default(now())
  modifiedAt                 DateTime @updatedAt
}

model PaymentProviderWebhookEvent {
  paymentProviderWebhookEventId String   @id
  idAtProvider                  String   @unique
  data                          Json
  providerType                  String
  createdAt                     DateTime @default(now())
  modifiedAt                    DateTime @updatedAt
}

model WebhookEvent {
  webhookEventId               String    @id
  marketplaceId                String
  marketplaceWebhookEndpointId String
  refundId                     String?
  orderId                      String?
  paymentId                    String?
  type                         String
  status                       String
  failReason                   String?
  fails                        Int       @db.SmallInt
  lastAttemptAt                DateTime?
  nextRetryAt                  DateTime?
  data                         Json
  url                          String
  createdAt                    DateTime  @default(now())
  updatedAt                    DateTime  @default(now()) @updatedAt

  marketplace                Marketplace                @relation(fields: [marketplaceId], references: [marketplaceId])
  marketplaceWebhookEndpoint MarketplaceWebhookEndpoint @relation(fields: [marketplaceWebhookEndpointId], references: [marketplaceWebhookEndpointId])
  refund                     Refund?                    @relation(fields: [refundId], references: [refundId])
  order                      Order?                     @relation(fields: [orderId], references: [orderId])
  payment                    Payment?                   @relation(fields: [paymentId], references: [paymentId])
}

model Ban {
  banId         String  @id
  marketplaceId String
  fingerprint   String?
  email         String?
  reason        String?

  createdAt  DateTime @default(now())
  modifiedAt DateTime @default(now()) @updatedAt

  marketplace Marketplace @relation(fields: [marketplaceId], references: [marketplaceId])

  @@unique([email, marketplaceId])
  @@unique([fingerprint, marketplaceId])
}

model BanWhitelist {
  banWhitelistId String   @id
  marketplaceId  String
  fingerprint    String?  @unique
  email          String?  @unique
  createdAt      DateTime @default(now())
  modifiedAt     DateTime @default(now()) @updatedAt

  marketplace Marketplace @relation(fields: [marketplaceId], references: [marketplaceId])

  @@unique([email, marketplaceId])
  @@unique([fingerprint, marketplaceId])
}

model PaymentMethod {
  paymentMethodId       String  @id
  type                  String
  cardLast4             String?
  cardExpiryMonth       Int?
  cardExpiryYear        Int?
  idAtProvider          String
  providerType          String
  fingerprintAtProvider String? @unique

  createdAt  DateTime @default(now())
  modifiedAt DateTime @default(now()) @updatedAt

  payments Payment[]
}
