<!DOCTYPE html>
<html>
<head>
    <!-- ===================== Template Meta ===================== -->
    <!-- This file is a Handlebars‑style template used by generative‑AI tooling
         to create repeatable Software‑License reports.  Replace {{ … }} tokens
         (or re‑map them for your preferred engine) with real values at render
         time.  Keep the overall structure and CSS so downstream consumers get a
         visually consistent document. -->

    <title>Third-Party Software License Report</title>

    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; color: #333; }
        h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { color: #2c3e50; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        h3 { color: #3498db; }
        h4 { color: #2980b9; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; color: #2c3e50; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .high-risk { color: #e74c3c; font-weight: bold; }
        .medium-risk { color: #f39c12; font-weight: bold; }
        .low-risk { color: #27ae60; }
        .disclaimer { font-style: italic; border-top: 1px solid #ddd; padding-top: 20px; margin-top: 40px; color: #7f8c8d; }
        .recommendation { background-color: #eaf7fb; padding: 15px; border-left: 4px solid #3498db; margin: 20px 0; }
        ul, ol { margin-bottom: 15px; }
        li { margin-bottom: 5px; }
    </style>
</head>
<body>
    <!-- ===================== Header ===================== -->
    <h1>Third-Party Software License Report</h1>
    <p>This report provides an analysis of third-party software licenses used in this project, including their distribution, implications, and potential compliance issues for commercial use.</p>

    <!-- ===================== Section 1: Distribution Table ===================== -->
    <h2>1. License Distribution Overview</h2>
    <table>
        <tr>
            <th>License Type</th>
            <th>Count</th>
            <th>Risk Level</th>
        </tr>
        <tr>
      <td>MIT</td>
      <td>936</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>ISC</td>
      <td>70</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>Apache-2.0</td>
      <td>35</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>BSD-3-Clause</td>
      <td>24</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>BSD-2-Clause</td>
      <td>19</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>(MIT OR CC0-1.0)</td>
      <td>4</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>Unlicense</td>
      <td>3</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>BlueOak-1.0.0</td>
      <td>2</td>
      <td class="unknown-risk">Unknown</td>
    </tr><tr>
      <td>0BSD</td>
      <td>2</td>
      <td class="low-risk">Low</td>
    </tr><tr>
      <td>UNKNOWN</td>
      <td>1</td>
      <td class="unknown-risk">Unknown</td>
    </tr><tr>
      <td>Python-2.0</td>
      <td>1</td>
      <td class="unknown-risk">Unknown</td>
    </tr><tr>
      <td>CC-BY-4.0</td>
      <td>1</td>
      <td class="unknown-risk">Unknown</td>
    </tr><tr>
      <td>UNLICENSED</td>
      <td>1</td>
      <td class="unknown-risk">Unknown</td>
    </tr><tr>
      <td>(BSD-2-Clause OR MIT OR Apache-2.0)</td>
      <td>1</td>
      <td class="low-risk">Low</td>
    </tr>
    </table>

    <!-- ===================== Section 2: License Categories ===================== -->
    <h2>2. License Categories and Implications</h2>

    <h3>Low-Risk Licenses</h3>
    <p>Low-risk licenses are permissive and impose minimal restrictions on software usage, modification, and distribution. They typically allow for commercial use with few obligations.</p><h4>MIT</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, private use</li>
        <li><strong>Limitations</strong>: No liability, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice must be included</li>
        <li><strong>Commercial Implications</strong>: Very permissive license with minimal requirements. Safe for most commercial applications.</li>
      </ul><h4>Apache-2.0</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, patent use, private use</li>
        <li><strong>Limitations</strong>: No liability, no trademark use, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice, state changes, patent grant</li>
        <li><strong>Commercial Implications</strong>: Business-friendly license that explicitly grants patent rights. Requires documentation of changes but doesn't require code sharing.</li>
      </ul><h4>ISC</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, private use</li>
        <li><strong>Limitations</strong>: No liability, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice must be included</li>
        <li><strong>Commercial Implications</strong>: Similar to MIT, very permissive with minimal requirements.</li>
      </ul><h4>BSD-3-Clause</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, private use</li>
        <li><strong>Limitations</strong>: No liability, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice must be included, cannot use contributors' names for promotion</li>
        <li><strong>Commercial Implications</strong>: Permissive license with minimal requirements, safe for commercial use.</li>
      </ul><h3>Medium-Risk Licenses</h3>
    <p>Medium-risk licenses include reciprocal or copyleft provisions that may require sharing modifications under certain conditions. They generally allow commercial use but have more specific requirements.</p><h4>MPL-2.0</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, patent use, private use</li>
        <li><strong>Limitations</strong>: No liability, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice, same license (file-level), disclose source</li>
        <li><strong>Commercial Implications</strong>: Weak copyleft license that requires modifications to specific files to be shared, but allows linking with proprietary code.</li>
      </ul><h4>LGPL-3.0</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, patent use, private use</li>
        <li><strong>Limitations</strong>: No liability, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice, same license (library), disclose source, state changes</li>
        <li><strong>Commercial Implications</strong>: Allows linking with proprietary software but modifications to the library itself must be shared.</li>
      </ul><h3>High-Risk Licenses</h3>
    <p>High-risk licenses contain strong copyleft provisions that may require sharing all source code of the entire application. These can significantly impact commercial software distribution and may require legal review.</p><h4>GPL-3.0</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, patent use, private use</li>
        <li><strong>Limitations</strong>: No liability, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice, same license, disclose source, state changes</li>
        <li><strong>Commercial Implications</strong>: Strong copyleft license that requires all derivative works to be distributed under the same license. May require sharing your entire application source code.</li>
      </ul><h4>AGPL-3.0</h4>
      <ul>
        <li><strong>Permissions</strong>: Commercial use, modification, distribution, patent use, private use</li>
        <li><strong>Limitations</strong>: No liability, no warranty</li>
        <li><strong>Conditions</strong>: License and copyright notice, same license, disclose source, state changes, network use is distribution</li>
        <li><strong>Commercial Implications</strong>: Strongest copyleft license that considers network use as distribution. Using AGPL code in a web service requires making the entire source code available.</li>
      </ul>
    {{/each}}

    <!-- ===================== Section 3: Packages with Issues ===================== -->
    <h2>3. Packages with Potential Issues</h2>

    <p>No high or medium risk packages were identified in this project.</p>
    {{/each}}

    <!-- ===================== Section 4: Conclusions ===================== -->
    <h2>4. Conclusion and Recommendations</h2>

    <p>This project uses 1100 third-party packages with various licenses. 99% of packages use low-risk licenses, 0% use medium-risk licenses, and 0% use high-risk licenses.</p>
    <ol>
        <li>The majority of dependencies (1094 packages, 99%) use permissive licenses like MIT, Apache-2.0, and ISC, which pose minimal risk for commercial use.</li><li>0 packages (0%) use licenses with moderate restrictions that may require additional compliance measures.</li><li>0 packages (0%) use licenses with strong copyleft provisions that may require careful review.</li>
    </ol>

    <div class="recommendation">
        <h3>Recommendations:</h3>
        <ol>
            <li>Review and address any high-risk dependencies identified in this report.</li><li>Establish a process for license compliance review before adding new dependencies.</li><li>Consider creating an approved license list for future development.</li><li>Maintain documentation of all third-party components and their licenses.</li><li>Consult with legal counsel for specific guidance on license compliance for your commercial products.</li>
        </ol>
    </div>

    <!-- ===================== Section 5: Modules List ===================== -->
    <h2>5. Complete List of Modules</h2>
    <p>A complete list of all modules and their licenses is available in the accompanying CSV file.</p>
    <p><em>The full list has been omitted from this report for brevity.</em></p>

    <!-- ===================== Disclaimer ===================== -->
    <p class="disclaimer">This report is provided for informational purposes only and does not constitute legal advice. Consult with legal counsel for specific guidance on license compliance for your products.</p>
</body>
</html>
