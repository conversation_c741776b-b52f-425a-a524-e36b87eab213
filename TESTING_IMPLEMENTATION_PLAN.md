# Jest Unit Testing Implementation Plan
## Payment Hub - Nest.js DDD Backend

### Testing Scope Definition

#### INCLUDE for Testing (High Business Value):
- **Business Logic & Domain Services**: All domain services, business rules, and complex calculations
- **Application Services & Use Cases**: All use cases containing business logic and orchestration
- **Complex Data Transformations**: Fee calculations, order totals, payment processing logic
- **Validation Logic**: Business rules, custom validators, and domain constraints
- **Repository Implementations**: Complex queries and data transformation logic
- **Custom Decorators & Guards**: Authentication, authorization, and business logic guards
- **Utility Functions**: API key generation, ULID validation, RabbitMQ configuration
- **Domain Rules**: All business rules implementing IBusinessRule interface
- **Event Handlers**: Business logic in event processing
- **Infrastructure Services**: Complex adapters and service implementations

#### EXCLUDE from Testing (Low Business Value):
- **Simple DTOs**: Basic data transfer objects without logic
- **Basic HTTP Error Classes**: Simple error classes and constants
- **Configuration Files**: Environment variables and static configuration
- **Simple Entity Classes**: Basic entity structures without business methods
- **Basic CRUD Operations**: Simple repository operations without business logic
- **Infrastructure Setup**: Database connections and module configurations
- **Type Definitions**: TypeScript interfaces and type definitions

### Implementation Strategy

The implementation follows a 3-phase approach prioritizing business-critical functionality:

#### Phase 1: Core Business Logic & Order Management (Priority 1)
**Target: Order processing, payment rules, and fee calculations**

**Order Module:**
- `src/modules/order/application/use-cases/create-order/create-order.use-case.ts` → `create-order.use-case.spec.ts`
- `src/modules/order/application/use-cases/update-order/update-order.use-case.ts` → `update-order.use-case.spec.ts`
- `src/modules/order/application/use-cases/get-order/get-order.use-case.ts` → `get-order.use-case.spec.ts`

**Payment Module:**
- `src/modules/payment/application/use-cases/validate-payment-card/validate-payment-card.use-case.ts` → `validate-payment-card.use-case.spec.ts`
- `src/modules/payment/application/use-cases/get-payment/get-payment.use-case.ts` → `get-payment.use-case.spec.ts`
- `src/modules/payment/application/use-cases/get-payment-by-session-id/get-payment-by-session-id.use-case.ts` → `get-payment-by-session-id.use-case.spec.ts`
- `src/modules/payment/domain/rules/duplicate-card-cool-down.rule.ts` → `duplicate-card-cool-down.rule.spec.ts`
- `src/modules/payment/domain/rules/payment-cannot-have-pending-refunds.rule.ts` → `payment-cannot-have-pending-refunds.rule.spec.ts`
- `src/modules/payment/domain/rules/payment-is-succeeded-or-partially-refunded.rule.ts` → `payment-is-succeeded-or-partially-refunded.rule.spec.ts`
- `src/modules/payment/domain/rules/refund-amount-must-be-smaller-than-remaining-amount.rule.ts` → `refund-amount-must-be-smaller-than-remaining-amount.rule.spec.ts`
- `src/modules/payment/domain/rules/refund-marketplace-fee-must-be-smaller-than-remaining-marketplace-fee.rule.ts` → `refund-marketplace-fee-must-be-smaller-than-remaining-marketplace-fee.rule.spec.ts`

**Payment Session Module:**
- `src/modules/payment-session/application/use-cases/create-payment-session/create-payment-session.use-case.ts` → `create-payment-session.use-case.spec.ts`
- `src/modules/payment-session/application/use-cases/get-payment-session/get-payment-session.use-case.ts` → `get-payment-session.use-case.spec.ts`
- `src/modules/payment-session/domain/rules/payment-session-total-with-fees-must-match.rule.ts` → `payment-session-total-with-fees-must-match.rule.spec.ts`

**Refund Module:**
- `src/modules/refund/application/use-cases/create-refund/create-refund.use-case.ts` → `create-refund.use-case.spec.ts`
- `src/modules/refund/application/use-cases/get-refund/get-refund.use-case.ts` → `get-refund.use-case.spec.ts`

#### Phase 2: Application Services & Domain Logic (Priority 2)
**Target: Application services, ban management, and domain services**

**Customer Module:**
- `src/modules/customer/application/services/customer.application-service.ts` → `customer.application-service.spec.ts`
- `src/modules/customer/application/use-cases/create-customer/create-customer.use-case.ts` → `create-customer.use-case.spec.ts`
- `src/modules/customer/application/use-cases/get-customer/get-customer.use-case.ts` → `get-customer.use-case.spec.ts`
- `src/modules/customer/application/use-cases/update-customer/update-customer.use-case.ts` → `update-customer.use-case.spec.ts`

**Ban Module:**
- `src/modules/ban/application/services/ban.application-service.ts` → `ban.application-service.spec.ts`
- `src/modules/ban/application/services/ban-checker.application-service.ts` → `ban-checker.application-service.spec.ts`
- `src/modules/ban/application/use-cases/validate-fingerprint-and-email-by-pos-id/validate-fingerprint-by-pos-id.use-case.ts` → `validate-fingerprint-by-pos-id.use-case.spec.ts`

**Ban Whitelist Module:**
- `src/modules/ban-whitelist/application/services/ban-whitelist.application-service.ts` → `ban-whitelist.application-service.spec.ts`

**Point of Sale Module:**
- `src/modules/point-of-sale/application/services/point-of-sale.application-service.ts` → `point-of-sale.application-service.spec.ts`
- `src/modules/point-of-sale/application/use-cases/create-point-of-sale/create-point-of-sale.use-case.ts` → `create-point-of-sale.use-case.spec.ts`
- `src/modules/point-of-sale/application/use-cases/update-point-of-sale/update-point-of-sale.use-case.ts` → `update-point-of-sale.use-case.spec.ts`
- `src/modules/point-of-sale/application/use-cases/get-point-of-sale/get-point-of-sale.use-case.ts` → `get-point-of-sale.use-case.spec.ts`
- `src/modules/point-of-sale/application/use-cases/validate-entry-code/validate-entry-code.use-case.ts` → `validate-entry-code.use-case.spec.ts`
- `src/modules/point-of-sale/domain/rules/end-date-after-start-date.rule.ts` → `end-date-after-start-date.rule.spec.ts`
- `src/modules/point-of-sale/domain/rules/sales-started.rule.ts` → `sales-started.rule.spec.ts`
- `src/modules/point-of-sale/domain/rules/sales-not-ended.rule.ts` → `sales-not-ended.rule.spec.ts`
- `src/modules/point-of-sale/domain/rules/point-of-sale-is-published.rule.ts` → `point-of-sale-is-published.rule.spec.ts`

**Webhook Event Module:**
- `src/modules/webhook-event/domain/services/webhook.domain-service.ts` → `webhook.domain-service.spec.ts`
- `src/modules/webhook-event/domain/rules/webhook-event-must-not-be-delivered.rule.ts` → `webhook-event-must-not-be-delivered.rule.spec.ts`

**Custom Field Module:**
- `src/modules/custom-field/domain/services/custom-field.domain-service.ts` → `custom-field.domain-service.spec.ts`

#### Phase 3: Product Management & Infrastructure (Priority 3)
**Target: Product/ticket management, marketplace, and infrastructure components**

**Product Module:**
- `src/modules/product/application/use-cases/create-product/create-product.use-case.ts` → `create-product.use-case.spec.ts`
- `src/modules/product/application/use-cases/update-product/update-product.use-case.ts` → `update-product.use-case.spec.ts`
- `src/modules/product/application/use-cases/get-product/get-product.use-case.ts` → `get-product.use-case.spec.ts`
- `src/modules/product/application/use-cases/list-products/list-products.use-case.ts` → `list-products.use-case.spec.ts`

**Product Category Module:**
- `src/modules/product-category/application/use-cases/create-product-category/create-product-category.use-case.ts` → `create-product-category.use-case.spec.ts`
- `src/modules/product-category/application/use-cases/update-product-category/update-product-category.use-case.ts` → `update-product-category.use-case.spec.ts`

**Ticket Module:**
- `src/modules/ticket/application/use-cases/create-ticket/create-ticket.use-case.ts` → `create-ticket.use-case.spec.ts`
- `src/modules/ticket/application/use-cases/update-ticket/update-ticket.use-case.ts` → `update-ticket.use-case.spec.ts`
- `src/modules/ticket/application/use-cases/get-ticket/get-ticket.use-case.ts` → `get-ticket.use-case.spec.ts`
- `src/modules/ticket/application/use-cases/list-ticket/list-ticket.use-case.ts` → `list-ticket.use-case.spec.ts`
- `src/modules/ticket/domain/rules/duplicate-ticket-purchase.rule.ts` → `duplicate-ticket-purchase.rule.spec.ts`

**Marketplace Module:**
- `src/modules/marketplace/application/use-cases/create-marketplace/create-marketplace.use-case.ts` → `create-marketplace.use-case.spec.ts`



**Payment Method Module:**
- `src/modules/payment-method/application/use-cases/create-payment-method/create-payment-method.use-case.ts` → `create-payment-method.use-case.spec.ts`
- `src/modules/payment-method/application/use-cases/get-payment-method/get-payment-method.use-case.ts` → `get-payment-method.use-case.spec.ts`

**Payment Provider Account Module:**
- `src/modules/payment-provider-account/application/use-cases/create-payment-provider-account/create-payment-provider-account.use-case.ts` → `create-payment-provider-account.use-case.spec.ts`
- `src/modules/payment-provider-account/application/use-cases/update-payment-provider-account/update-payment-provider-account.use-case.ts` → `update-payment-provider-account.use-case.spec.ts`

**Payment Provider Customer Module:**
- `src/modules/payment-provider-customer/application/use-cases/create-payment-provider-customer/create-payment-provider-customer.use-case.ts` → `create-payment-provider-customer.use-case.spec.ts`

**Payment Provider Webhook Event Module:**
- `src/modules/payment-provider-webhook-event/application/use-cases/process-dispute-created-event/process-dispute-created-event.use-case.ts` → `process-dispute-created-event.use-case.spec.ts`

**Webhook Endpoints Module:**
- `src/modules/webhook-endpoints/application/use-cases/create-webhook-endpoint/create-webhook-endpoint.use-case.ts` → `create-webhook-endpoint.use-case.spec.ts`
- `src/modules/webhook-endpoints/application/use-cases/update-webhook-endpoint/update-webhook-endpoint.use-case.ts` → `update-webhook-endpoint.use-case.spec.ts`

**Webhook Event Module:**
- `src/modules/webhook-event/application/use-cases/create-webhook-event/create-webhook-event.use-case.ts` → `create-webhook-event.use-case.spec.ts`
- `src/modules/webhook-event/application/use-cases/send-webhook-event/send-webhook-event.use-case.ts` → `send-webhook-event.use-case.spec.ts`

**Infrastructure Components:**
- `src/infra/guards/marketplace-api-key.guard.ts` → `marketplace-api-key.guard.spec.ts`
- `src/core/infra/validators/isUlid.ts` → `isUlid.spec.ts`
- `src/core/infra/utils/api-key.util.ts` → `api-key.util.spec.ts`
- `src/core/infra/utils/rabbitmq.util.ts` → `rabbitmq.util.spec.ts`
- `src/infra/drivers/prisma/prisma.utils.ts` → `prisma.utils.spec.ts`


